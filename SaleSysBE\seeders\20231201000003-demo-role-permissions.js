'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // L<PERSON>y danh sách vai trò và quyền
    const roles = await queryInterface.sequelize.query(
      'SELECT id, ma_vai_tro FROM vai_tro',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const permissions = await queryInterface.sequelize.query(
      'SELECT id, ma_quyen FROM quyen',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const rolePermissions = [];

    // ADMIN - có tất cả quyền
    const adminRole = roles.find(r => r.ma_vai_tro === 'ADMIN');
    if (adminRole) {
      permissions.forEach(permission => {
        rolePermissions.push({
          vai_tro_id: adminRole.id,
          quyen_id: permission.id
        });
      });
    }

    // QUAN_LY - c<PERSON> hầu hết quyền trừ cấu hình hệ thống
    const managerRole = roles.find(r => r.ma_vai_tro === 'QUAN_LY');
    if (managerRole) {
      const managerPermissions = permissions.filter(p => 
        p.ma_quyen !== 'CAU_HINH_HE_THONG' && 
        !p.ma_quyen.includes('XOA_NGUOI_DUNG') &&
        !p.ma_quyen.includes('XOA_VAI_TRO') &&
        !p.ma_quyen.includes('XOA_QUYEN')
      );
      
      managerPermissions.forEach(permission => {
        rolePermissions.push({
          vai_tro_id: managerRole.id,
          quyen_id: permission.id
        });
      });
    }

    // NHAN_VIEN_BAN_HANG - quyền liên quan đến bán hàng
    const salesRole = roles.find(r => r.ma_vai_tro === 'NHAN_VIEN_BAN_HANG');
    if (salesRole) {
      const salesPermissions = permissions.filter(p => 
        p.ma_quyen.includes('XEM_SAN_PHAM') ||
        p.ma_quyen.includes('DON_HANG') ||
        p.ma_quyen.includes('KHACH_HANG') ||
        p.ma_quyen === 'XEM_BAO_CAO'
      );
      
      salesPermissions.forEach(permission => {
        rolePermissions.push({
          vai_tro_id: salesRole.id,
          quyen_id: permission.id
        });
      });
    }

    // NHAN_VIEN_KHO - quyền liên quan đến kho hàng
    const warehouseRole = roles.find(r => r.ma_vai_tro === 'NHAN_VIEN_KHO');
    if (warehouseRole) {
      const warehousePermissions = permissions.filter(p => 
        p.ma_quyen.includes('SAN_PHAM') ||
        p.ma_quyen.includes('KHO_HANG') ||
        p.ma_quyen === 'XEM_DON_HANG' ||
        p.ma_quyen === 'SUA_DON_HANG'
      );
      
      warehousePermissions.forEach(permission => {
        rolePermissions.push({
          vai_tro_id: warehouseRole.id,
          quyen_id: permission.id
        });
      });
    }

    // KE_TOAN - quyền liên quan đến báo cáo và tài chính
    const accountantRole = roles.find(r => r.ma_vai_tro === 'KE_TOAN');
    if (accountantRole) {
      const accountantPermissions = permissions.filter(p => 
        p.ma_quyen.includes('BAO_CAO') ||
        p.ma_quyen === 'XEM_DON_HANG' ||
        p.ma_quyen === 'XEM_KHACH_HANG' ||
        p.ma_quyen === 'XEM_SAN_PHAM'
      );
      
      accountantPermissions.forEach(permission => {
        rolePermissions.push({
          vai_tro_id: accountantRole.id,
          quyen_id: permission.id
        });
      });
    }

    if (rolePermissions.length > 0) {
      await queryInterface.bulkInsert('vai_tro_quyen', rolePermissions, {});
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('vai_tro_quyen', null, {});
  }
};
