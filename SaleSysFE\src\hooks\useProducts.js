import { useQuery, useMutation, useQueryClient } from 'react-query';
import { message } from 'antd';
import { productsAPI, testAPI } from '../services/api';

// Query keys
export const PRODUCT_QUERY_KEYS = {
  all: ['products'],
  lists: () => [...PRODUCT_QUERY_KEYS.all, 'list'],
  list: (filters) => [...PRODUCT_QUERY_KEYS.lists(), filters],
  details: () => [...PRODUCT_QUERY_KEYS.all, 'detail'],
  detail: (id) => [...PRODUCT_QUERY_KEYS.details(), id],
  categories: () => [...PRODUCT_QUERY_KEYS.all, 'categories'],
  brands: () => [...PRODUCT_QUERY_KEYS.all, 'brands'],
  tags: () => [...PRODUCT_QUERY_KEYS.all, 'tags'],
  attributes: () => [...PRODUCT_QUERY_KEYS.all, 'attributes'],
  variants: (productId) => [...PRODUCT_QUERY_KEYS.all, 'variants', productId],
};

// Get products list
export const useProducts = (params = {}) => {
  return useQuery(
    PRODUCT_QUERY_KEYS.list(params),
    () => testAPI.getProducts(params),
    {
      select: (response) => {
        console.log('🛍️ Products API response:', response);
        // API test trả về trực tiếp data, không cần response.data.data
        return response;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};

// Get single product
export const useProduct = (id) => {
  return useQuery(
    PRODUCT_QUERY_KEYS.detail(id),
    () => productsAPI.getProduct(id),
    {
      enabled: !!id,
      select: (response) => response.data.data,
    }
  );
};

// Get product stock history
export const useProductStockHistory = (productId, params = {}) => {
  return useQuery(
    [...PRODUCT_QUERY_KEYS.detail(productId), 'stock-history', params],
    () => {
      // Tạm thời sử dụng test API không cần auth
      const queryParams = new URLSearchParams(params).toString();
      const url = `http://localhost:5000/api/test/products/${productId}/stock-history${queryParams ? '?' + queryParams : ''}`;
      return fetch(url).then(res => res.json());
    },
    {
      enabled: !!productId,
      select: (response) => {
        console.log('📦 Product Stock History API response:', response);
        return response;
      },
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
};

// Get variant stock history
export const useVariantStockHistory = (variantId, params = {}) => {
  return useQuery(
    ['variants', variantId, 'stock-history', params],
    () => {
      // Tạm thời sử dụng test API không cần auth
      const queryParams = new URLSearchParams(params).toString();
      const url = `http://localhost:5000/api/test/variants/${variantId}/stock-history${queryParams ? '?' + queryParams : ''}`;
      return fetch(url).then(res => res.json());
    },
    {
      enabled: !!variantId,
      select: (response) => {
        console.log('🔧 Variant Stock History API response:', response);
        return response;
      },
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
};

// Create product mutation
export const useCreateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation(productsAPI.createProduct, {
    onSuccess: (data) => {
      message.success('Tạo sản phẩm thành công!');
      queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.lists());
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi tạo sản phẩm');
    },
  });
};

// Update product mutation
export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, data }) => productsAPI.updateProduct(id, data),
    {
      onSuccess: (data, variables) => {
        message.success('Cập nhật sản phẩm thành công!');
        queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.lists());
        queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.detail(variables.id));
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật sản phẩm');
      },
    }
  );
};

// Delete product mutation
export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation(productsAPI.deleteProduct, {
    onSuccess: () => {
      message.success('Xóa sản phẩm thành công!');
      queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.lists());
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa sản phẩm');
    },
  });
};

// Get categories
export const useCategories = () => {
  return useQuery(
    PRODUCT_QUERY_KEYS.categories(),
    () => {
      // Tạm thời sử dụng test API không cần auth
      return fetch('http://localhost:5000/api/test/categories').then(res => res.json());
    },
    {
      select: (response) => {
        console.log('🏷️ Categories API response:', response);
        // API test trả về trực tiếp data
        return Array.isArray(response?.data) ? response.data : [];
      },
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};

// Create category mutation
export const useCreateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation(
    (data) => {
      return fetch('http://localhost:5000/api/test/categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      }).then(res => res.json());
    },
    {
      onSuccess: () => {
        message.success('Tạo loại sản phẩm thành công!');
        queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.categories());
      },
      onError: (error) => {
        message.error(error.message || 'Có lỗi xảy ra khi tạo loại sản phẩm');
      },
    }
  );
};

// Update category mutation
export const useUpdateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, data }) => {
      return fetch(`http://localhost:5000/api/test/categories/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      }).then(res => res.json());
    },
    {
      onSuccess: () => {
        message.success('Cập nhật loại sản phẩm thành công!');
        queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.categories());
      },
      onError: (error) => {
        message.error(error.message || 'Có lỗi xảy ra khi cập nhật loại sản phẩm');
      },
    }
  );
};

// Delete category mutation
export const useDeleteCategory = () => {
  const queryClient = useQueryClient();

  return useMutation(
    (id) => {
      return fetch(`http://localhost:5000/api/test/categories/${id}`, {
        method: 'DELETE'
      }).then(res => res.json());
    },
    {
      onSuccess: () => {
        message.success('Xóa loại sản phẩm thành công!');
        queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.categories());
      },
      onError: (error) => {
        message.error(error.message || 'Có lỗi xảy ra khi xóa loại sản phẩm');
      },
    }
  );
};

// Get brands
export const useBrands = () => {
  return useQuery(
    PRODUCT_QUERY_KEYS.brands(),
    () => {
      // Tạm thời sử dụng test API không cần auth
      return fetch('http://localhost:5000/api/test/brands').then(res => res.json());
    },
    {
      select: (response) => {
        console.log('🏢 Brands API response:', response);
        // API test trả về trực tiếp data
        return Array.isArray(response?.data) ? response.data : [];
      },
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};

// Create brand mutation
export const useCreateBrand = () => {
  const queryClient = useQueryClient();

  return useMutation(
    (data) => {
      return fetch('http://localhost:5000/api/test/brands', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      }).then(res => res.json());
    },
    {
      onSuccess: () => {
        message.success('Tạo nhãn hiệu thành công!');
        queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.brands());
      },
      onError: (error) => {
        message.error(error.message || 'Có lỗi xảy ra khi tạo nhãn hiệu');
      },
    }
  );
};

// Update brand mutation
export const useUpdateBrand = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, data }) => {
      return fetch(`http://localhost:5000/api/test/brands/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      }).then(res => res.json());
    },
    {
      onSuccess: () => {
        message.success('Cập nhật nhãn hiệu thành công!');
        queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.brands());
      },
      onError: (error) => {
        message.error(error.message || 'Có lỗi xảy ra khi cập nhật nhãn hiệu');
      },
    }
  );
};

// Delete brand mutation
export const useDeleteBrand = () => {
  const queryClient = useQueryClient();

  return useMutation(
    (id) => {
      return fetch(`http://localhost:5000/api/test/brands/${id}`, {
        method: 'DELETE'
      }).then(res => res.json());
    },
    {
      onSuccess: () => {
        message.success('Xóa nhãn hiệu thành công!');
        queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.brands());
      },
      onError: (error) => {
        message.error(error.message || 'Có lỗi xảy ra khi xóa nhãn hiệu');
      },
    }
  );
};

// Get tags
export const useTags = () => {
  return useQuery(
    PRODUCT_QUERY_KEYS.tags(),
    productsAPI.getTags,
    {
      select: (response) => {
        console.log('🏷️ Tags raw response:', response);
        console.log('🏷️ Tags response structure:', {
          hasResponse: !!response,
          hasData: !!response?.data,
          hasDataData: !!response?.data?.data,
          dataType: typeof response?.data?.data,
          isArray: Array.isArray(response?.data?.data),
          dataLength: response?.data?.data?.length,
          firstItem: response?.data?.data?.[0]
        });
        return Array.isArray(response?.data?.data) ? response.data.data : [];
      },
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};

// Get attributes
export const useAttributes = () => {
  return useQuery(
    PRODUCT_QUERY_KEYS.attributes(),
    productsAPI.getAttributes,
    {
      select: (response) => {
        console.log('🔧 Attributes raw response:', response);
        console.log('🔧 Attributes response structure:', {
          hasResponse: !!response,
          hasData: !!response?.data,
          hasDataData: !!response?.data?.data,
          dataType: typeof response?.data?.data,
          isArray: Array.isArray(response?.data?.data),
          dataLength: response?.data?.data?.length,
          firstItem: response?.data?.data?.[0]
        });
        return Array.isArray(response?.data?.data) ? response.data.data : [];
      },
      staleTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};

// Get product variants for a specific product
export const useProductVariants = (productId) => {
  return useQuery(
    PRODUCT_QUERY_KEYS.variants(productId),
    () => productsAPI.getVariants(productId),
    {
      enabled: !!productId,
      select: (data) => data.data,
    }
  );
};

// Get all product variants (for search/selection)
export const useAllProductVariants = (params = {}) => {
  return useQuery(
    ['product-variants', params],
    () => {
      console.log('🔧 Fetching product variants with params:', params);
      return testAPI.getProductVariants(params);
    },
    {
      select: (response) => {
        console.log('🔧 Product Variants API response:', response);
        // API test trả về trực tiếp data
        if (response?.success && Array.isArray(response?.data)) {
          console.log('🔧 Product Variants count:', response.data.length);
          return response.data;
        }
        console.warn('🔧 Product Variants: Invalid response format', response);
        return [];
      },
      staleTime: 2 * 60 * 1000, // 2 minutes
      enabled: true, // Always enabled
      retry: 3,
      onError: (error) => {
        console.error('🔧 Product Variants API error:', error);
      }
    }
  );
};

// Create variant mutation
export const useCreateVariant = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ productId, data }) => productsAPI.createVariant(productId, data),
    {
      onSuccess: (data, variables) => {
        message.success('Tạo phiên bản sản phẩm thành công!');
        queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.variants(variables.productId));
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi tạo phiên bản sản phẩm');
      },
    }
  );
};

// Update variant mutation
export const useUpdateVariant = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ productId, variantId, data }) => productsAPI.updateVariant(productId, variantId, data),
    {
      onSuccess: (data, variables) => {
        message.success('Cập nhật phiên bản sản phẩm thành công!');
        queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.variants(variables.productId));
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật phiên bản sản phẩm');
      },
    }
  );
};

// Delete variant mutation
export const useDeleteVariant = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ productId, variantId }) => productsAPI.deleteVariant(productId, variantId),
    {
      onSuccess: (data, variables) => {
        message.success('Xóa phiên bản sản phẩm thành công!');
        queryClient.invalidateQueries(PRODUCT_QUERY_KEYS.variants(variables.productId));
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa phiên bản sản phẩm');
      },
    }
  );
};
