'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.createTable('phieu_kiem_ke', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      ma_kiem_ke: {
        type: Sequelize.STRING(50),
        allowNull: false,
        unique: true,
        comment: 'Mã phiếu kiểm kê duy nhất'
      },
      ten_kiem_ke: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'Tên phiếu kiểm kê'
      },
      kho_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'kho_hang',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
        comment: 'ID kho hàng được kiểm kê'
      },
      trang_thai: {
        type: Sequelize.ENUM('ke_hoach', 'dang_thuc_hien', 'hoan_thanh', 'huy'),
        allowNull: false,
        defaultValue: 'ke_hoach',
        comment: 'Trạng thái kiểm kê'
      },
      ngay_bat_dau: {
        type: Sequelize.DATE,
        allowNull: false,
        comment: 'Ngày bắt đầu kiểm kê'
      },
      ngay_ket_thuc: {
        type: Sequelize.DATE,
        allowNull: true,
        comment: 'Ngày kết thúc kiểm kê'
      },
      nguoi_kiem_ke: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'Người thực hiện kiểm kê'
      },
      tong_san_pham: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Tổng số sản phẩm cần kiểm kê'
      },
      san_pham_da_kiem: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Số sản phẩm đã kiểm kê'
      },
      san_pham_lech: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Số sản phẩm có chênh lệch'
      },
      gia_tri_lech: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0,
        comment: 'Giá trị chênh lệch (có thể âm)'
      },
      ghi_chu: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Ghi chú về phiếu kiểm kê'
      },
      nguoi_tao: {
        type: Sequelize.STRING(100),
        allowNull: false,
        comment: 'Người tạo phiếu'
      },
      nguoi_cap_nhap: {
        type: Sequelize.STRING(100),
        allowNull: true,
        comment: 'Người cập nhật cuối'
      },
      ngay_tao: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      ngay_cap_nhap: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('phieu_kiem_ke', ['ma_kiem_ke']);
    await queryInterface.addIndex('phieu_kiem_ke', ['kho_hang_id']);
    await queryInterface.addIndex('phieu_kiem_ke', ['trang_thai']);
    await queryInterface.addIndex('phieu_kiem_ke', ['ngay_bat_dau']);
    await queryInterface.addIndex('phieu_kiem_ke', ['nguoi_kiem_ke']);
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.dropTable('phieu_kiem_ke');
  }
};
