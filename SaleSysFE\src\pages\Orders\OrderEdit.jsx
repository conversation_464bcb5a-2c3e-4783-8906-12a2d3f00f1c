import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Card, 
  Form, 
  Input, 
  Select, 
  Button, 
  Space, 
  Typography, 
  Row,
  Col,
  Spin,
  Alert,
  message,
  Table,
  InputNumber,
  Popconfirm
} from 'antd';
import { 
  ArrowLeftOutlined, 
  SaveOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { testAPI } from '../../services/api';
import { ORDER_STATUS, ORDER_STATUS_LABELS } from '../../constants/orderStatus';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// Hook để lấy chi tiết đơn hàng
const useOrderDetail = (id) => {
  return useQuery(
    ['order', id],
    async () => {
      const response = await fetch(`http://localhost:5000/api/test/orders/${id}`);
      if (!response.ok) {
        throw new Error('<PERSON>hông thể lấy thông tin đơn hàng');
      }
      return response.json();
    },
    {
      enabled: !!id,
      retry: 1,
      refetchOnWindowFocus: false
    }
  );
};

const OrderEdit = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const [products, setProducts] = useState([]);
  
  const { data, isLoading, error } = useOrderDetail(id);

  // Mutation để cập nhật đơn hàng
  const updateOrderMutation = useMutation(
    (orderData) => testAPI.updateOrder(id, orderData),
    {
      onSuccess: () => {
        message.success('Cập nhật đơn hàng thành công!');
        queryClient.invalidateQueries(['order', id]);
        queryClient.invalidateQueries(['orders']);
        navigate(`/orders/${id}`);
      },
      onError: (error) => {
        message.error(`Lỗi: ${error.message}`);
      }
    }
  );

  // Khởi tạo form khi có dữ liệu
  useEffect(() => {
    if (data?.data) {
      const order = data.data;
      form.setFieldsValue({
        ma_don_hang: order.ma_don_hang,
        trang_thai: order.trang_thai,
        nguon_don_hang: order.nguon_don_hang,
        ghi_chu: order.ghi_chu,
        khach_hang_ho_ten: order.khach_hang?.ho_ten,
        khach_hang_so_dien_thoai: order.khach_hang?.so_dien_thoai,
        khach_hang_email: order.khach_hang?.email,
        chiet_khau: order.chiet_khau
      });
      setProducts(order.san_pham_list || []);
    }
  }, [data, form]);

  // Xử lý submit form
  const handleSubmit = async (values) => {
    try {
      const orderData = {
        ...values,
        san_pham_list: products,
        tong_tien: calculateTotal(),
        tong_phai_tra: calculateTotal() - (values.chiet_khau || 0)
      };
      
      await updateOrderMutation.mutateAsync(orderData);
    } catch (error) {
      console.error('Error updating order:', error);
    }
  };

  // Tính tổng tiền
  const calculateTotal = () => {
    return products.reduce((total, product) => {
      return total + (product.so_luong * product.don_gia);
    }, 0);
  };

  // Cập nhật sản phẩm
  const updateProduct = (index, field, value) => {
    const newProducts = [...products];
    newProducts[index] = {
      ...newProducts[index],
      [field]: value
    };
    
    // Tính lại thành tiền
    if (field === 'so_luong' || field === 'don_gia') {
      newProducts[index].thanh_tien = newProducts[index].so_luong * newProducts[index].don_gia;
    }
    
    setProducts(newProducts);
  };

  // Xóa sản phẩm
  const removeProduct = (index) => {
    const newProducts = products.filter((_, i) => i !== index);
    setProducts(newProducts);
  };

  // Cấu hình cột cho bảng sản phẩm
  const productColumns = [
    {
      title: 'STT',
      key: 'index',
      width: 60,
      render: (_, __, index) => index + 1
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'ten_san_pham',
      key: 'ten_san_pham',
      render: (value, record, index) => (
        <Input
          value={value}
          onChange={(e) => updateProduct(index, 'ten_san_pham', e.target.value)}
          placeholder="Tên sản phẩm"
        />
      )
    },
    {
      title: 'Số lượng',
      dataIndex: 'so_luong',
      key: 'so_luong',
      width: 120,
      render: (value, record, index) => (
        <InputNumber
          value={value}
          onChange={(val) => updateProduct(index, 'so_luong', val)}
          min={1}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Đơn giá',
      dataIndex: 'don_gia',
      key: 'don_gia',
      width: 150,
      render: (value, record, index) => (
        <InputNumber
          value={value}
          onChange={(val) => updateProduct(index, 'don_gia', val)}
          min={0}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value.replace(/\$\s?|(,*)/g, '')}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Thành tiền',
      dataIndex: 'thanh_tien',
      key: 'thanh_tien',
      width: 150,
      render: (value) => new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(value)
    },
    {
      title: 'Thao tác',
      key: 'action',
      width: 80,
      render: (_, record, index) => (
        <Popconfirm
          title="Bạn có chắc chắn muốn xóa sản phẩm này?"
          onConfirm={() => removeProduct(index)}
          okText="Xóa"
          cancelText="Hủy"
        >
          <Button 
            type="text" 
            danger 
            icon={<DeleteOutlined />}
            size="small"
          />
        </Popconfirm>
      )
    }
  ];

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Lỗi"
        description={error.message}
        type="error"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  const order = data?.data;
  if (!order) {
    return (
      <Alert
        message="Không tìm thấy đơn hàng"
        type="warning"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate(`/orders/${id}`)}
          >
            Quay lại
          </Button>
          <Title level={3} style={{ margin: 0 }}>
            Chỉnh sửa đơn hàng {order.ma_don_hang}
          </Title>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Row gutter={[24, 24]}>
          {/* Thông tin đơn hàng */}
          <Col span={16}>
            <Card title="Thông tin đơn hàng" style={{ marginBottom: '24px' }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="Mã đơn hàng"
                    name="ma_don_hang"
                  >
                    <Input disabled />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Trạng thái"
                    name="trang_thai"
                    rules={[{ required: true, message: 'Vui lòng chọn trạng thái!' }]}
                  >
                    <Select>
                      {Object.entries(ORDER_STATUS).map(([value, label]) => (
                        <Option key={value} value={value}>
                          {ORDER_STATUS_LABELS[value]}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Nguồn đơn hàng"
                    name="nguon_don_hang"
                  >
                    <Select allowClear>
                      <Option value="truc_tiep">Trực tiếp</Option>
                      <Option value="online">Online</Option>
                      <Option value="dien_thoai">Điện thoại</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Chiết khấu"
                    name="chiet_khau"
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                      parser={value => value.replace(/\$\s?|(,*)/g, '')}
                    />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <Form.Item
                    label="Ghi chú"
                    name="ghi_chu"
                  >
                    <TextArea rows={3} />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Danh sách sản phẩm */}
            <Card 
              title="Danh sách sản phẩm"
              extra={
                <Button 
                  type="dashed" 
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setProducts([...products, {
                      id: Date.now(),
                      ten_san_pham: '',
                      so_luong: 1,
                      don_gia: 0,
                      thanh_tien: 0
                    }]);
                  }}
                >
                  Thêm sản phẩm
                </Button>
              }
            >
              <Table
                columns={productColumns}
                dataSource={products}
                rowKey="id"
                pagination={false}
                size="small"
              />
              
              <div style={{ marginTop: '16px', textAlign: 'right' }}>
                <Space direction="vertical" size="small">
                  <div>
                    <strong>Tổng tiền hàng: </strong>
                    {new Intl.NumberFormat('vi-VN', {
                      style: 'currency',
                      currency: 'VND'
                    }).format(calculateTotal())}
                  </div>
                  <div>
                    <strong>Chiết khấu: </strong>
                    {new Intl.NumberFormat('vi-VN', {
                      style: 'currency',
                      currency: 'VND'
                    }).format(form.getFieldValue('chiet_khau') || 0)}
                  </div>
                  <div style={{ fontSize: '16px', color: '#1890ff' }}>
                    <strong>Tổng phải trả: </strong>
                    {new Intl.NumberFormat('vi-VN', {
                      style: 'currency',
                      currency: 'VND'
                    }).format(calculateTotal() - (form.getFieldValue('chiet_khau') || 0))}
                  </div>
                </Space>
              </div>
            </Card>
          </Col>

          {/* Thông tin khách hàng */}
          <Col span={8}>
            <Card title="Thông tin khách hàng">
              <Form.Item
                label="Tên khách hàng"
                name="khach_hang_ho_ten"
              >
                <Input />
              </Form.Item>
              <Form.Item
                label="Số điện thoại"
                name="khach_hang_so_dien_thoai"
              >
                <Input />
              </Form.Item>
              <Form.Item
                label="Email"
                name="khach_hang_email"
              >
                <Input />
              </Form.Item>
            </Card>
          </Col>
        </Row>

        {/* Buttons */}
        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <Space>
            <Button onClick={() => navigate(`/orders/${id}`)}>
              Hủy
            </Button>
            <Button 
              type="primary" 
              htmlType="submit"
              icon={<SaveOutlined />}
              loading={updateOrderMutation.isLoading}
            >
              Lưu thay đổi
            </Button>
          </Space>
        </div>
      </Form>
    </div>
  );
};

export default OrderEdit;
