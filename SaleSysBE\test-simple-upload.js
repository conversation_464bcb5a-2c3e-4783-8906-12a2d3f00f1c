const { simpleUploadImage } = require('./utils/simpleCloudinaryUpload');

async function testSimpleUpload() {
  try {
    console.log('🧪 Testing simple upload...');

    // Create a simple test image (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x5C, 0xC2, 0x8A, 0xBC, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    const result = await simpleUploadImage(testImageBuffer, {
      folder: 'test-simple',
      width: 400,
      height: 300,
      public_id: `test_simple_${Date.now()}`
    });

    if (result.success) {
      console.log('✅ Simple upload test successful!');
      console.log('📸 Image URL:', result.data.url);
      console.log('🔗 Public ID:', result.data.public_id);
      console.log('🖼️ Thumbnail:', result.data.thumbnail_url);
      console.log('🎨 Optimized:', result.data.optimized_url);
    } else {
      console.log('❌ Simple upload test failed:', result.error);
    }

  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    process.exit(0);
  }
}

// Run test
testSimpleUpload();
