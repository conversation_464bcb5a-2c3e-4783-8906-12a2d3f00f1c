import React, { useEffect } from 'react';
import { Modal, Form, Input, Select, Switch, Row, Col, Statistic, Card } from 'antd';
import { ShopOutlined, EnvironmentOutlined, FileTextOutlined, CheckCircleOutlined } from '@ant-design/icons';

const { TextArea } = Input;
const { Option } = Select;

const WarehouseForm = ({ 
  visible, 
  onCancel, 
  onSubmit, 
  initialData, 
  loading,
  mode = 'create' // 'create', 'edit', 'view'
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible) {
      if (initialData) {
        form.setFieldsValue({
          ten_kho: initialData.ten_kho,
          dia_chi: initialData.dia_chi,
          mo_ta: initialData.mo_ta,
          trang_thai: initialData.trang_thai === 'active'
        });
      } else {
        form.resetFields();
      }
    }
  }, [visible, initialData, form]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const submitData = {
        ...values,
        trang_thai: values.trang_thai ? 'active' : 'inactive'
      };
      onSubmit(submitData);
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const isViewMode = mode === 'view';
  const isEditMode = mode === 'edit';
  const isCreateMode = mode === 'create';

  const getTitle = () => {
    switch (mode) {
      case 'create': return 'Thêm kho hàng mới';
      case 'edit': return 'Sửa thông tin kho hàng';
      case 'view': return 'Chi tiết kho hàng';
      default: return 'Kho hàng';
    }
  };

  return (
    <Modal
      title={getTitle()}
      open={visible}
      onCancel={onCancel}
      onOk={isViewMode ? onCancel : handleSubmit}
      okText={isViewMode ? 'Đóng' : (isCreateMode ? 'Tạo kho hàng' : 'Cập nhật')}
      cancelText={isViewMode ? null : 'Hủy'}
      confirmLoading={loading}
      width={800}
      destroyOnClose
    >
      {/* Statistics for view/edit mode */}
      {(isViewMode || isEditMode) && initialData && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={8}>
            <Card size="small">
              <Statistic
                title="Tổng sản phẩm"
                value={initialData.tong_san_pham || 0}
                prefix={<ShopOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small">
              <Statistic
                title="Tổng tồn kho"
                value={initialData.tong_ton_kho || 0}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card size="small">
              <Statistic
                title="Trạng thái"
                value={initialData.trang_thai === 'active' ? 'Hoạt động' : 'Ngừng hoạt động'}
                valueStyle={{ 
                  color: initialData.trang_thai === 'active' ? '#52c41a' : '#ff4d4f' 
                }}
              />
            </Card>
          </Col>
        </Row>
      )}

      <Form
        form={form}
        layout="vertical"
        initialValues={{
          trang_thai: true
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Tên kho hàng"
              name="ten_kho"
              rules={[
                { required: true, message: 'Vui lòng nhập tên kho hàng!' },
                { min: 2, message: 'Tên kho hàng phải có ít nhất 2 ký tự!' },
                { max: 100, message: 'Tên kho hàng không được quá 100 ký tự!' }
              ]}
            >
              <Input
                prefix={<ShopOutlined />}
                placeholder="Nhập tên kho hàng"
                disabled={isViewMode}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Trạng thái"
              name="trang_thai"
              valuePropName="checked"
            >
              <Switch
                checkedChildren="Hoạt động"
                unCheckedChildren="Ngừng hoạt động"
                disabled={isViewMode}
              />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          label="Địa chỉ"
          name="dia_chi"
          rules={[
            { required: true, message: 'Vui lòng nhập địa chỉ kho hàng!' },
            { min: 5, message: 'Địa chỉ phải có ít nhất 5 ký tự!' },
            { max: 255, message: 'Địa chỉ không được quá 255 ký tự!' }
          ]}
        >
          <Input
            prefix={<EnvironmentOutlined />}
            placeholder="Nhập địa chỉ kho hàng"
            disabled={isViewMode}
          />
        </Form.Item>

        <Form.Item
          label="Mô tả"
          name="mo_ta"
          rules={[
            { max: 500, message: 'Mô tả không được quá 500 ký tự!' }
          ]}
        >
          <TextArea
            rows={4}
            placeholder="Nhập mô tả về kho hàng (tùy chọn)"
            disabled={isViewMode}
            showCount
            maxLength={500}
          />
        </Form.Item>

        {/* Additional info for view mode */}
        {isViewMode && initialData && (
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Người tạo">
                <Input value={initialData.nguoi_tao || 'N/A'} disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Ngày tạo">
                <Input 
                  value={initialData.ngay_tao ? new Date(initialData.ngay_tao).toLocaleString('vi-VN') : 'N/A'} 
                  disabled 
                />
              </Form.Item>
            </Col>
          </Row>
        )}
      </Form>
    </Modal>
  );
};

export default WarehouseForm;
