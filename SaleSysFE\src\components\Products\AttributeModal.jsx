import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Button,
  Space,
  Typography,
  Tag,
  message,
  Divider
} from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

const AttributeModal = ({
  visible,
  onCancel,
  onSave,
  editingAttribute
}) => {
  const [form] = Form.useForm();
  const [attributeValues, setAttributeValues] = useState([]);
  const [newValue, setNewValue] = useState('');

  useEffect(() => {
    if (visible) {
      if (editingAttribute) {
        // Edit mode
        form.setFieldsValue({
          ten: editingAttribute.ten,
          mo_ta: editingAttribute.mo_ta
        });
        setAttributeValues(editingAttribute.gia_tri || []);
      } else {
        // Create mode
        form.resetFields();
        setAttributeValues([]);
      }
    }
  }, [visible, editingAttribute, form]);

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      if (attributeValues.length === 0) {
        message.error('Vui lòng thêm ít nhất một giá trị thuộc tính');
        return;
      }

      const attributeData = {
        ...values,
        gia_tri: attributeValues
      };

      onSave(attributeData, editingAttribute?.index);
      handleCancel();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setAttributeValues([]);
    setNewValue('');
    onCancel();
  };

  const addValue = () => {
    if (newValue.trim() && !attributeValues.includes(newValue.trim())) {
      setAttributeValues([...attributeValues, newValue.trim()]);
      setNewValue('');
    } else if (attributeValues.includes(newValue.trim())) {
      message.warning('Giá trị này đã tồn tại');
    }
  };

  const removeValue = (valueToRemove) => {
    setAttributeValues(attributeValues.filter(value => value !== valueToRemove));
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addValue();
    }
  };

  return (
    <Modal
      title={editingAttribute ? "Sửa thuộc tính" : "Thêm thuộc tính"}
      open={visible}
      onCancel={handleCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Hủy
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          {editingAttribute ? "Cập nhật" : "Thêm"}
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="ten"
          label="Tên thuộc tính"
          rules={[{ required: true, message: 'Vui lòng nhập tên thuộc tính' }]}
        >
          <Input placeholder="Ví dụ: Màu sắc, Kích thước, Chất liệu..." />
        </Form.Item>

        <Form.Item
          name="mo_ta"
          label="Mô tả"
        >
          <Input.TextArea 
            rows={3} 
            placeholder="Mô tả về thuộc tính này..." 
          />
        </Form.Item>

        <Divider>Giá trị thuộc tính</Divider>

        <div style={{ marginBottom: 16 }}>
          <Space.Compact style={{ width: '100%' }}>
            <Input
              placeholder="Nhập giá trị thuộc tính"
              value={newValue}
              onChange={(e) => setNewValue(e.target.value)}
              onKeyPress={handleKeyPress}
            />
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={addValue}
              disabled={!newValue.trim()}
            >
              Thêm
            </Button>
          </Space.Compact>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            Nhấn Enter hoặc click "Thêm" để thêm giá trị
          </Text>
        </div>

        <div style={{ marginBottom: 16 }}>
          <Text strong>Danh sách giá trị ({attributeValues.length}):</Text>
          <div style={{ marginTop: 8, minHeight: 40, border: '1px dashed #d9d9d9', borderRadius: 6, padding: 8 }}>
            {attributeValues.length === 0 ? (
              <Text type="secondary">Chưa có giá trị nào</Text>
            ) : (
              <Space wrap>
                {attributeValues.map((value, index) => (
                  <Tag
                    key={index}
                    closable
                    onClose={() => removeValue(value)}
                    color="blue"
                  >
                    {value}
                  </Tag>
                ))}
              </Space>
            )}
          </div>
        </div>

        <div style={{ background: '#f6f6f6', padding: 12, borderRadius: 6 }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <strong>Lưu ý:</strong> Thuộc tính sẽ được sử dụng để tạo các phiên bản khác nhau của sản phẩm. 
            Ví dụ: Thuộc tính "Màu sắc" với các giá trị "Đỏ", "Xanh", "Vàng" sẽ tạo ra các phiên bản 
            sản phẩm có màu khác nhau.
          </Text>
        </div>
      </Form>
    </Modal>
  );
};

export default AttributeModal;
