'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('vai_tro_quyen', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      vai_tro_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'vai_tro',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      quyen_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'quyen',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      }
    });

    // Check if index already exists before creating
    const indexes = await queryInterface.showIndex('vai_tro_quyen');
    const indexExists = indexes.some(index => index.name === 'vai_tro_quyen_unique');

    if (!indexExists) {
      await queryInterface.addIndex('vai_tro_quyen', ['vai_tro_id', 'quyen_id'], {
        unique: true,
        name: 'vai_tro_quyen_unique'
      });
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('vai_tro_quyen');
  }
};
