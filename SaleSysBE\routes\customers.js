const express = require('express');
const router = express.Router();
const { asyncHandler } = require('../middleware/errorHandler');
const { requirePermission } = require('../middleware/auth');
const {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  exportCustomers,
  importCustomers
} = require('../controllers/customerController');

/**
 * @route GET /api/customers
 * @desc Lấy danh sách khách hàng
 * @access Private (Cần quyền XEM_KHACH_HANG)
 */
router.get('/', requirePermission('XEM_KHACH_HANG'), asyncHandler(getCustomers));

/**
 * @route GET /api/customers/export
 * @desc Xuất danh sách khách hàng
 * @access Private (Cần quyền XEM_KHACH_HANG)
 */
router.get('/export', requirePermission('XEM_KHACH_HANG'), asyncHandler(exportCustomers));

/**
 * @route POST /api/customers/import
 * @desc Nhập danh sách khách hàng từ file
 * @access Private (Cần quyền THEM_KHACH_HANG)
 */
router.post('/import', requirePermission('THEM_KHACH_HANG'), asyncHandler(importCustomers));

/**
 * @route GET /api/customers/:id
 * @desc Lấy thông tin chi tiết khách hàng
 * @access Private (Cần quyền XEM_KHACH_HANG)
 */
router.get('/:id', requirePermission('XEM_KHACH_HANG'), asyncHandler(getCustomer));

/**
 * @route POST /api/customers
 * @desc Tạo khách hàng mới
 * @access Private (Cần quyền THEM_KHACH_HANG)
 */
router.post('/', requirePermission('THEM_KHACH_HANG'), asyncHandler(createCustomer));

/**
 * @route PUT /api/customers/:id
 * @desc Cập nhật khách hàng
 * @access Private (Cần quyền SUA_KHACH_HANG)
 */
router.put('/:id', requirePermission('SUA_KHACH_HANG'), asyncHandler(updateCustomer));

/**
 * @route DELETE /api/customers/:id
 * @desc Xóa khách hàng
 * @access Private (Cần quyền XOA_KHACH_HANG)
 */
router.delete('/:id', requirePermission('XOA_KHACH_HANG'), asyncHandler(deleteCustomer));

module.exports = router;
