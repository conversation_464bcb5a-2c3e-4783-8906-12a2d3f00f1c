const express = require('express');
const { asyncHandler, AppError } = require('../middleware/errorHandler');
const { requirePermission } = require('../middleware/auth');
const { getOrders, getOrder, createOrder, updateOrder } = require('../controllers/orderController');

const router = express.Router();

/**
 * @route GET /api/orders
 * @desc Lấy danh sách đơn hàng
 * @access Private (Cần quyền XEM_DON_HANG)
 */
router.get('/', requirePermission('XEM_DON_HANG'), asyncHandler(getOrders));

/**
 * @route GET /api/orders/:id
 * @desc Lấy thông tin chi tiết đơn hàng
 * @access Private (Cần quyền XEM_DON_HANG)
 */
router.get('/:id', requirePermission('XEM_DON_HANG'), asyncHandler(getOrder));

/**
 * @route POST /api/orders
 * @desc Tạo đơn hàng mới
 * @access Private (Cần quyền THEM_DON_HANG)
 */
router.post('/', requirePermission('THEM_DON_HANG'), asyncHandler(createOrder));

/**
 * @route PUT /api/orders/:id
 * @desc Cập nhật đơn hàng
 * @access Private (Cần quyền SUA_DON_HANG)
 */
router.put('/:id', requirePermission('SUA_DON_HANG'), asyncHandler(updateOrder));

/**
 * @route DELETE /api/orders/:id
 * @desc Xóa đơn hàng
 * @access Private (Cần quyền XOA_DON_HANG)
 */
router.delete('/:id', requirePermission('XOA_DON_HANG'), asyncHandler(async (req, res) => {
  // TODO: Implement order deletion
  res.json({
    success: true,
    message: 'API xóa đơn hàng đang được phát triển'
  });
}));

module.exports = router;
