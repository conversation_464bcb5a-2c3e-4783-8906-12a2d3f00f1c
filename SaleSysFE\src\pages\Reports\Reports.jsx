import React from 'react';
import { Card, Typography, Result, Button } from 'antd';
import { BarChartOutlined } from '@ant-design/icons';

const { Title } = Typography;

const Reports = () => {
  return (
    <div>
      <Card>
        <Title level={3} style={{ marginBottom: 24 }}>
          Báo cáo
        </Title>
        
        <Result
          icon={<BarChartOutlined style={{ color: '#1890ff' }} />}
          title="Module Báo cáo"
          subTitle="Tính năng báo cáo đang được phát triển. Sẽ bao gồm: báo cáo doanh thu, báo cáo bán h<PERSON>ng, báo cáo tồn kho, báo cáo kh<PERSON>ch hàng, xuất báo cáo Excel/PDF."
          extra={
            <Button type="primary" disabled>
              Đang phát triển
            </Button>
          }
        />
      </Card>
    </div>
  );
};

export default Reports;
