import React, { useState } from 'react';
import {
  Modal,
  Table,
  Button,
  Input,
  Space,
  Typography,
  Checkbox,
  message
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined
} from '@ant-design/icons';
import {
  useAvailableProducts,
  useAddProductToStockCheck
} from '../hooks/useWarehouses';

const { Text } = Typography;

const AddProductsModal = ({ 
  visible, 
  onCancel, 
  stockCheckId,
  onSuccess 
}) => {
  const [searchText, setSearchText] = useState('');
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // API hooks
  const { 
    data: productsResponse = { data: [], pagination: {} }, 
    isLoading 
  } = useAvailableProducts(stockCheckId, {
    page: currentPage,
    limit: pageSize,
    search: searchText
  });

  const addProductsMutation = useAddProductToStockCheck();
  console.log("data",productsResponse);
  const products = productsResponse.data || [];
  const pagination = productsResponse.pagination || {};

  // Handle search
  const handleSearch = (value) => {
    setSearchText(value);
    setCurrentPage(1);
    setSelectedProducts([]); // Clear selection when searching
  };

  // Handle select products
  const handleSelectProduct = (productId, checked) => {
    if (checked) {
      setSelectedProducts([...selectedProducts, productId]);
    } else {
      setSelectedProducts(selectedProducts.filter(id => id !== productId));
    }
  };

  // Handle select all
  const handleSelectAll = (checked) => {
    if (checked) {
      const allProductIds = products.map(p => p.phien_ban_id);
      setSelectedProducts(allProductIds);
    } else {
      setSelectedProducts([]);
    }
  };

  // Handle add products
  const handleAddProducts = async () => {
    if (selectedProducts.length === 0) {
      message.warning('Vui lòng chọn ít nhất một sản phẩm');
      return;
    }

    try {
      await addProductsMutation.mutateAsync({
        id: stockCheckId,
        phien_ban_san_pham_ids: selectedProducts
      });
      
      setSelectedProducts([]);
      onSuccess?.();
      onCancel();
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  // Handle cancel
  const handleCancel = () => {
    setSelectedProducts([]);
    setSearchText('');
    setCurrentPage(1);
    onCancel();
  };

  // Table columns
  const columns = [
    {
      title: (
        <Checkbox
          checked={selectedProducts.length === products.length && products.length > 0}
          indeterminate={selectedProducts.length > 0 && selectedProducts.length < products.length}
          onChange={(e) => handleSelectAll(e.target.checked)}
        >
          Chọn tất cả
        </Checkbox>
      ),
      key: 'select',
      width: 120,
      render: (_, record) => (
        <Checkbox
          checked={selectedProducts.includes(record.phien_ban_id)}
          onChange={(e) => handleSelectProduct(record.phien_ban_id, e.target.checked)}
        />
      )
    },
    {
      title: 'Sản phẩm',
      key: 'product',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.san_pham_ten}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.phien_ban_ten} - {record.ma_sku}
          </Text>
        </div>
      )
    },
    {
      title: 'Tồn kho',
      dataIndex: 'so_luong_ton',
      key: 'so_luong_ton',
      width: 100,
      align: 'center',
      render: (value) => (
        <Text style={{ fontWeight: 500 }}>{value?.toLocaleString('vi-VN')}</Text>
      )
    },
    {
      title: 'Giá vốn',
      dataIndex: 'gia_von',
      key: 'gia_von',
      width: 120,
      align: 'right',
      render: (value) => (
        <Text>{value?.toLocaleString('vi-VN')}₫</Text>
      )
    }
  ];

  return (
    <Modal
      title="Thêm sản phẩm vào phiếu kiểm kê"
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Hủy
        </Button>,
        <Button
          key="add"
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddProducts}
          loading={addProductsMutation.isLoading}
          disabled={selectedProducts.length === 0}
        >
          Thêm {selectedProducts.length > 0 ? `(${selectedProducts.length})` : ''} sản phẩm
        </Button>
      ]}
    >
      <div style={{ marginBottom: 16 }}>
        <Input
          placeholder="Tìm kiếm sản phẩm..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => handleSearch(e.target.value)}
          allowClear
        />
      </div>

      {selectedProducts.length > 0 && (
        <div style={{ marginBottom: 16, padding: '8px 12px', backgroundColor: '#f0f9ff', borderRadius: '6px' }}>
          <Text type="secondary">
            Đã chọn {selectedProducts.length} sản phẩm
          </Text>
        </div>
      )}

      <Table
        columns={columns}
        dataSource={products}
        loading={isLoading}
        rowKey="phien_ban_id"
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: pagination.total || 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `${range[0]}-${range[1]} của ${total} sản phẩm`,
          onChange: (page, size) => {
            setCurrentPage(page);
            setPageSize(size);
            setSelectedProducts([]); // Clear selection when changing page
          },
        }}
        scroll={{ y: 400 }}
        size="small"
      />
    </Modal>
  );
};

export default AddProductsModal;
