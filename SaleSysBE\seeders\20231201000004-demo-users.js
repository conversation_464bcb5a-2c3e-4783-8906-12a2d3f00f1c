'use strict';
const bcrypt = require('bcryptjs');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const hashedPassword = await bcrypt.hash('123456', 12);
    
    await queryInterface.bulkInsert('nguoi_dung', [
      {
        ho_ten: 'Admin System',
        email: '<EMAIL>',
        mat_khau: hashedPassword,
        so_dien_thoai: '0123456789',
        loai_nguoi_dung: 'admin',
        trang_thai: 'dang_giao_dich',
        nguoi_tao: 'system',
        nguoi_cap_nhap: 'system',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ho_ten: 'Nguyễn <PERSON>',
        email: '<EMAIL>',
        mat_khau: hashedPassword,
        so_dien_thoai: '0123456790',
        loai_nguoi_dung: 'nhan_vien',
        trang_thai: 'dang_giao_dich',
        nguoi_tao: 'system',
        nguoi_cap_nhap: 'system',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ho_ten: 'Trần Thị Bán Hàng',
        email: '<EMAIL>',
        mat_khau: hashedPassword,
        so_dien_thoai: '0123456791',
        loai_nguoi_dung: 'nhan_vien',
        trang_thai: 'dang_giao_dich',
        nguoi_tao: 'system',
        nguoi_cap_nhap: 'system',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ho_ten: 'Lê Văn Kho',
        email: '<EMAIL>',
        mat_khau: hashedPassword,
        so_dien_thoai: '0123456792',
        loai_nguoi_dung: 'nhan_vien',
        trang_thai: 'dang_giao_dich',
        nguoi_tao: 'system',
        nguoi_cap_nhap: 'system',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ho_ten: 'Phạm Thị Kế Toán',
        email: '<EMAIL>',
        mat_khau: hashedPassword,
        so_dien_thoai: '0123456793',
        loai_nguoi_dung: 'nhan_vien',
        trang_thai: 'dang_giao_dich',
        nguoi_tao: 'system',
        nguoi_cap_nhap: 'system',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ho_ten: 'Nguyễn Văn Khách',
        email: '<EMAIL>',
        mat_khau: hashedPassword,
        so_dien_thoai: '0987654321',
        loai_nguoi_dung: 'khach_hang',
        trang_thai: 'dang_giao_dich',
        nguoi_tao: 'system',
        nguoi_cap_nhap: 'system',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ho_ten: 'Trần Thị Mua',
        email: '<EMAIL>',
        mat_khau: hashedPassword,
        so_dien_thoai: '0987654322',
        loai_nguoi_dung: 'khach_hang',
        trang_thai: 'dang_giao_dich',
        nguoi_tao: 'system',
        nguoi_cap_nhap: 'system',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('nguoi_dung', null, {});
  }
};
