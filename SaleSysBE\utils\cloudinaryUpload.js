const cloudinary = require('../config/cloudinary');
const fs = require('fs');
const path = require('path');

/**
 * Upload ảnh lên Cloudinary từ file buffer hoặc file path
 * @param {Buffer|String} fileInput - Buffer của file hoặc đường dẫn file
 * @param {Object} options - T<PERSON><PERSON> chọn upload
 * @param {String} options.folder - Thư mục trên Cloudinary (vd: 'products', 'users')
 * @param {String} options.public_id - ID công khai (tùy chọn)
 * @param {Number} options.width - Chiều rộng resize (tùy chọn)
 * @param {Number} options.height - Chiều cao resize (tùy chọn)
 * @param {String} options.crop - Kiểu crop ('fill', 'fit', 'auto', etc.)
 * @param {Number} options.quality - Chất lượng ảnh (1-100)
 * @returns {Promise<Object>} Kết quả upload với URL và thông tin ảnh
 */
async function uploadImage(fileInput, options = {}) {
  try {
    const {
      folder = 'general',
      public_id,
      width,
      height,
      crop = 'fill',
      quality = 'auto',
      format = 'jpg'
    } = options;

    // Cấu hình upload
    const uploadOptions = {
      folder: `salesys/${folder}`, // Prefix với tên project
      resource_type: 'image',
      quality: quality,
    };

    // Thêm public_id nếu có
    if (public_id) {
      uploadOptions.public_id = public_id;
    }

    // Thêm transformation nếu có width/height
    if (width || height) {
      uploadOptions.transformation = {
        width: width,
        height: height,
        crop: crop,
        quality: 'auto'
      };
    }

    let uploadResult;

    // Kiểm tra input là Buffer hay file path
    if (Buffer.isBuffer(fileInput)) {
      // Upload từ buffer
      uploadResult = await new Promise((resolve, reject) => {
        cloudinary.uploader.upload_stream(
          uploadOptions,
          (error, result) => {
            if (error) reject(error);
            else resolve(result);
          }
        ).end(fileInput);
      });
    } else if (typeof fileInput === 'string') {
      // Upload từ file path hoặc URL
      uploadResult = await cloudinary.uploader.upload(fileInput, uploadOptions);
    } else {
      throw new Error('File input phải là Buffer hoặc string path');
    }

    // Tạo các URL tối ưu
    const optimizedUrl = cloudinary.url(uploadResult.public_id, {
      quality: 'auto',
      width: width || 800,
      height: height || 600,
      crop: 'fill',
      format: 'jpg' // Sử dụng format cố định
    });

    const thumbnailUrl = cloudinary.url(uploadResult.public_id, {
      quality: 'auto',
      width: 200,
      height: 200,
      crop: 'fill',
      format: 'jpg' // Sử dụng format cố định
    });

    return {
      success: true,
      data: {
        public_id: uploadResult.public_id,
        url: uploadResult.secure_url,
        optimized_url: optimizedUrl,
        thumbnail_url: thumbnailUrl,
        width: uploadResult.width,
        height: uploadResult.height,
        format: uploadResult.format,
        size: uploadResult.bytes,
        created_at: uploadResult.created_at
      }
    };

  } catch (error) {
    console.error('Lỗi upload ảnh lên Cloudinary:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Upload nhiều ảnh cùng lúc
 * @param {Array} files - Mảng các file (Buffer hoặc path)
 * @param {Object} options - Tùy chọn upload
 * @returns {Promise<Array>} Mảng kết quả upload
 */
async function uploadMultipleImages(files, options = {}) {
  try {
    const uploadPromises = files.map((file, index) => {
      const fileOptions = {
        ...options,
        public_id: options.public_id ? `${options.public_id}_${index + 1}` : undefined
      };
      return uploadImage(file, fileOptions);
    });

    const results = await Promise.all(uploadPromises);
    
    const successResults = results.filter(result => result.success);
    const failedResults = results.filter(result => !result.success);

    return {
      success: failedResults.length === 0,
      uploaded: successResults.length,
      failed: failedResults.length,
      data: successResults.map(result => result.data),
      errors: failedResults.map(result => result.error)
    };

  } catch (error) {
    console.error('Lỗi upload nhiều ảnh:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Xóa ảnh từ Cloudinary
 * @param {String} publicId - Public ID của ảnh cần xóa
 * @returns {Promise<Object>} Kết quả xóa
 */
async function deleteImage(publicId) {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    
    return {
      success: result.result === 'ok',
      data: result
    };
  } catch (error) {
    console.error('Lỗi xóa ảnh từ Cloudinary:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Tạo URL ảnh với transformation
 * @param {String} publicId - Public ID của ảnh
 * @param {Object} options - Tùy chọn transformation
 * @returns {String} URL ảnh đã transform
 */
function getTransformedUrl(publicId, options = {}) {
  const {
    width = 400,
    height = 300,
    crop = 'fill',
    quality = 'auto',
    format = 'jpg'
  } = options;

  return cloudinary.url(publicId, {
    width,
    height,
    crop,
    quality,
    format: format
  });
}

module.exports = {
  uploadImage,
  uploadMultipleImages,
  deleteImage,
  getTransformedUrl,
  cloudinary
};
