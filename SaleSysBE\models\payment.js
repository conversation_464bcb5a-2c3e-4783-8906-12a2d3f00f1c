'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Payment extends Model {
    static associate(models) {
      // Quan hệ với người dùng (n-1)
      Payment.belongsTo(models.NguoiDung, {
        foreignKey: 'customer_id',
        as: 'customer'
      });
    }
  }

  Payment.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    customer_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    type: {
      type: DataTypes.ENUM('thu', 'chi'),
      allowNull: false,
      comment: 'thu = thu tiền (gi<PERSON>m công nợ), chi = chi tiền (tăng công nợ)'
    },
    amount: {
      type: DataTypes.DOUBLE,
      allowNull: false,
      validate: {
        min: 0
      }
    },
    payment_method: {
      type: DataTypes.ENUM('cash', 'transfer', 'card', 'other'),
      allowNull: false,
      defaultValue: 'cash'
    },
    note: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    is_partial_payment: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
      comment: 'true = thanh toán một phần, false = thanh toán toàn bộ'
    },
    created_by: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('pending', 'completed', 'cancelled'),
      allowNull: false,
      defaultValue: 'pending'
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'Payment',
    tableName: 'payments',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at'
  });

  return Payment;
};
