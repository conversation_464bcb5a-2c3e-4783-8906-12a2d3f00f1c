const { sequelize } = require('./models');

async function showTables() {
  try {
    console.log('🔍 Đang kết nối database...');
    
    // Show all tables
    const [results] = await sequelize.query('SHOW TABLES');
    
    console.log('\n📋 DANH SÁCH BẢNG TRONG DATABASE:');
    console.log('=====================================');
    
    results.forEach((row, index) => {
      const tableName = Object.values(row)[0];
      console.log(`${(index + 1).toString().padStart(2, '0')}. ${tableName}`);
    });
    
    console.log('=====================================');
    console.log(`✅ Tổng cộng: ${results.length} bảng`);
    
    // Show table details for some important tables
    console.log('\n📊 CHI TIẾT MỘT SỐ BẢNG QUAN TRỌNG:');
    console.log('=====================================');
    
    const importantTables = [
      'nguoi_dung',
      'san_pham', 
      'phien_ban_san_pham',
      'don_hang',
      'kho_hang',
      'ton_kho_phien_ban'
    ];
    
    for (const tableName of importantTables) {
      try {
        const [tableInfo] = await sequelize.query(`DESCRIBE ${tableName}`);
        console.log(`\n🔸 Bảng: ${tableName.toUpperCase()}`);
        console.log(`   Số cột: ${tableInfo.length}`);
        
        // Show first few columns
        const columns = tableInfo.slice(0, 5).map(col => col.Field).join(', ');
        console.log(`   Cột: ${columns}${tableInfo.length > 5 ? '...' : ''}`);
        
        // Count records
        const [countResult] = await sequelize.query(`SELECT COUNT(*) as count FROM ${tableName}`);
        console.log(`   Số bản ghi: ${countResult[0].count}`);
        
      } catch (error) {
        console.log(`   ❌ Lỗi khi truy vấn bảng ${tableName}: ${error.message}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Lỗi kết nối database:', error.message);
  } finally {
    await sequelize.close();
    console.log('\n🔌 Đã đóng kết nối database');
  }
}

// Run the function
showTables();
