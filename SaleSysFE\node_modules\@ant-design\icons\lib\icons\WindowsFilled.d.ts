import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![windows](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUyMy44IDE5MS40djI4OC45aDM4MlYxMjguMXptMCA2NDIuMmwzODIgNjIuMnYtMzUyaC0zODJ6TTEyMC4xIDQ4MC4ySDQ0M1YyMDEuOWwtMzIyLjkgNTMuNXptMCAyOTAuNEw0NDMgODIzLjJWNTQzLjhIMTIwLjF6IiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
