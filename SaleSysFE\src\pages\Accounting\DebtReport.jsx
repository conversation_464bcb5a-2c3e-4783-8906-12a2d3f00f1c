import React, { useState } from 'react';
import { 
  <PERSON>, 
  Row, 
  Col, 
  Typography, 
  DatePicker, 
  Button, 
  Space, 
  Select,
  Table,
  Statistic,
  Progress,
  Divider
} from 'antd';
import { 
  Bar<PERSON>hartOutlined,
  PieChartOutlined,
  TrendingUpOutlined,
  DownloadOutlined,
  FilterOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { useQuery } from 'react-query';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

// Hook để lấy dữ liệu báo cáo
const useDebtReport = (filters) => {
  return useQuery(
    ['debt-report', filters],
    async () => {
      const queryParams = new URLSearchParams();
      
      if (filters.dateRange?.[0]) queryParams.append('start_date', filters.dateRange[0]);
      if (filters.dateRange?.[1]) queryParams.append('end_date', filters.dateRange[1]);
      if (filters.reportType) queryParams.append('type', filters.reportType);

      const response = await fetch(`http://localhost:5002/api/test/debt/report?${queryParams}`);
      if (!response.ok) {
        throw new Error('Không thể lấy dữ liệu báo cáo');
      }
      return response.json();
    },
    {
      keepPreviousData: true,
      refetchOnWindowFocus: false
    }
  );
};

const DebtReport = () => {
  const [filters, setFilters] = useState({
    dateRange: [dayjs().subtract(30, 'day'), dayjs()],
    reportType: 'overview'
  });

  const { data, isLoading } = useDebtReport(filters);

  // Dữ liệu mẫu cho biểu đồ
  const chartData = data?.chartData || [
    { month: 'T1', debt: 50000000, paid: 30000000 },
    { month: 'T2', debt: 45000000, paid: 35000000 },
    { month: 'T3', debt: 60000000, paid: 25000000 },
    { month: 'T4', debt: 55000000, paid: 40000000 },
    { month: 'T5', debt: 70000000, paid: 45000000 },
    { month: 'T6', debt: 65000000, paid: 50000000 }
  ];

  const pieData = data?.pieData || [
    { name: 'Đã thanh toán', value: 60, color: '#52c41a' },
    { name: 'Chưa thanh toán', value: 25, color: '#ff4d4f' },
    { name: 'Quá hạn', value: 15, color: '#ff7875' }
  ];

  const topDebtors = data?.topDebtors || [
    { rank: 1, customer: 'Nguyễn Văn A', debt: 15000000, orders: 5 },
    { rank: 2, customer: 'Trần Thị B', debt: 12000000, orders: 3 },
    { rank: 3, customer: 'Lê Văn C', debt: 10000000, orders: 4 },
    { rank: 4, customer: 'Phạm Thị D', debt: 8000000, orders: 2 },
    { rank: 5, customer: 'Hoàng Văn E', debt: 7000000, orders: 6 }
  ];

  const debtorColumns = [
    {
      title: 'Hạng',
      dataIndex: 'rank',
      key: 'rank',
      width: 60,
      align: 'center'
    },
    {
      title: 'Khách hàng',
      dataIndex: 'customer',
      key: 'customer'
    },
    {
      title: 'Công nợ',
      dataIndex: 'debt',
      key: 'debt',
      align: 'right',
      render: (value) => new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(value)
    },
    {
      title: 'Số đơn',
      dataIndex: 'orders',
      key: 'orders',
      align: 'center'
    }
  ];

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const stats = data?.stats || {
    totalDebt: 150000000,
    overdueDebt: 45000000,
    collectionRate: 75,
    avgDebtPerCustomer: 2500000
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <BarChartOutlined style={{ marginRight: '8px' }} />
          Báo cáo công nợ
        </Title>
      </div>

      {/* Filters */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]} align="middle">
          <Col span={6}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text strong>Khoảng thời gian</Text>
              <RangePicker
                value={filters.dateRange}
                onChange={(dates) => handleFilterChange('dateRange', dates)}
                format="DD/MM/YYYY"
                style={{ width: '100%' }}
              />
            </Space>
          </Col>
          <Col span={4}>
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <Text strong>Loại báo cáo</Text>
              <Select
                value={filters.reportType}
                onChange={(value) => handleFilterChange('reportType', value)}
                style={{ width: '100%' }}
              >
                <Option value="overview">Tổng quan</Option>
                <Option value="detailed">Chi tiết</Option>
                <Option value="aging">Phân tích độ tuổi nợ</Option>
              </Select>
            </Space>
          </Col>
          <Col span={6}>
            <Space>
              <Button icon={<FilterOutlined />}>
                Bộ lọc nâng cao
              </Button>
              <Button type="primary" icon={<DownloadOutlined />}>
                Xuất báo cáo
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Statistics Overview */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng công nợ"
              value={stats.totalDebt}
              formatter={(value) => new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              }).format(value)}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Nợ quá hạn"
              value={stats.overdueDebt}
              formatter={(value) => new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              }).format(value)}
              valueStyle={{ color: '#ff7875' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tỷ lệ thu hồi"
              value={stats.collectionRate}
              suffix="%"
              valueStyle={{ color: '#52c41a' }}
            />
            <Progress 
              percent={stats.collectionRate} 
              showInfo={false} 
              strokeColor="#52c41a"
              style={{ marginTop: '8px' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Nợ TB/Khách hàng"
              value={stats.avgDebtPerCustomer}
              formatter={(value) => new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              }).format(value)}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Charts */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={16}>
          <Card title="Biến động công nợ theo tháng">
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={chartData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis 
                  tickFormatter={(value) => 
                    new Intl.NumberFormat('vi-VN', {
                      notation: 'compact',
                      compactDisplay: 'short'
                    }).format(value)
                  }
                />
                <Tooltip 
                  formatter={(value) => 
                    new Intl.NumberFormat('vi-VN', {
                      style: 'currency',
                      currency: 'VND'
                    }).format(value)
                  }
                />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="debt" 
                  stackId="1"
                  stroke="#ff4d4f" 
                  fill="#ff4d4f"
                  name="Công nợ"
                />
                <Area 
                  type="monotone" 
                  dataKey="paid" 
                  stackId="1"
                  stroke="#52c41a" 
                  fill="#52c41a"
                  name="Đã thu"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col span={8}>
          <Card title="Tỷ lệ thanh toán">
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={pieData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {pieData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* Top Debtors */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="Top khách hàng có nợ cao nhất">
            <Table
              columns={debtorColumns}
              dataSource={topDebtors}
              rowKey="rank"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="Phân tích độ tuổi nợ">
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>0-30 ngày</Text>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Progress percent={45} showInfo={false} strokeColor="#52c41a" />
                    <Text>45%</Text>
                  </div>
                </div>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>31-60 ngày</Text>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Progress percent={30} showInfo={false} strokeColor="#faad14" />
                    <Text>30%</Text>
                  </div>
                </div>
                <div style={{ marginBottom: '16px' }}>
                  <Text strong>61-90 ngày</Text>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Progress percent={15} showInfo={false} strokeColor="#ff7875" />
                    <Text>15%</Text>
                  </div>
                </div>
                <div>
                  <Text strong>Trên 90 ngày</Text>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Progress percent={10} showInfo={false} strokeColor="#ff4d4f" />
                    <Text>10%</Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DebtReport;
