import React, { createContext, useContext } from 'react';
import { useAuth } from './AuthContext';

// Create context
const PermissionContext = createContext();

// Permission provider component
export const PermissionProvider = ({ children }) => {
  const { hasPermission, hasAnyPermission, hasRole, user, permissions } = useAuth();

  // Check if user is admin
  const isAdmin = () => {
    return user?.loai_nguoi_dung === 'admin';
  };

  // Check if user is manager
  const isManager = () => {
    return hasRole('QUAN_LY') || isAdmin();
  };

  // Check if user is staff
  const isStaff = () => {
    return user?.loai_nguoi_dung === 'nhan_vien' || isAdmin();
  };

  // Check if user is customer
  const isCustomer = () => {
    return user?.loai_nguoi_dung === 'khach_hang';
  };

  // Permission groups for easier management
  const permissionGroups = {
    // User management permissions
    userManagement: [
      'XEM_NGUOI_DUNG',
      'THEM_NGUOI_DUNG',
      'SUA_NGUOI_DUNG',
      'XOA_NGUOI_DUNG'
    ],

    // Role management permissions
    roleManagement: [
      'XEM_VAI_TRO',
      'THEM_VAI_TRO',
      'SUA_VAI_TRO',
      'XOA_VAI_TRO'
    ],

    // Permission management permissions
    permissionManagement: [
      'XEM_QUYEN',
      'THEM_QUYEN',
      'SUA_QUYEN',
      'XOA_QUYEN'
    ],

    // Product management permissions
    productManagement: [
      'XEM_SAN_PHAM',
      'THEM_SAN_PHAM',
      'SUA_SAN_PHAM',
      'XOA_SAN_PHAM'
    ],

    // Order management permissions
    orderManagement: [
      'XEM_DON_HANG',
      'THEM_DON_HANG',
      'SUA_DON_HANG',
      'XOA_DON_HANG'
    ],

    // Customer management permissions
    customerManagement: [
      'XEM_KHACH_HANG',
      'THEM_KHACH_HANG',
      'SUA_KHACH_HANG',
      'XOA_KHACH_HANG'
    ],

    // Warehouse management permissions
    warehouseManagement: [
      'XEM_KHO_HANG',
      'THEM_KHO_HANG',
      'SUA_KHO_HANG',
      'XOA_KHO_HANG'
    ],

    // Report permissions
    reports: [
      'XEM_BAO_CAO',
      'XUAT_BAO_CAO'
    ],

    // System configuration permissions
    systemConfig: [
      'CAU_HINH_HE_THONG'
    ]
  };

  // Check if user has any permission in a group
  const hasPermissionGroup = (groupName) => {
    const group = permissionGroups[groupName];
    if (!group) return false;
    return hasAnyPermission(group);
  };

  // Check if user can perform CRUD operations
  const canView = (resource) => {
    return hasPermission(`XEM_${resource.toUpperCase()}`);
  };

  const canCreate = (resource) => {
    return hasPermission(`THEM_${resource.toUpperCase()}`);
  };

  const canEdit = (resource) => {
    return hasPermission(`SUA_${resource.toUpperCase()}`);
  };

  const canDelete = (resource) => {
    return hasPermission(`XOA_${resource.toUpperCase()}`);
  };

  // Check if user can access specific modules
  const canAccessModule = (module) => {
    const modulePermissions = {
      dashboard: true, // Everyone can access dashboard
      users: canView('NGUOI_DUNG'),
      roles: canView('VAI_TRO'),
      permissions: canView('QUYEN'),
      products: canView('SAN_PHAM'),
      orders: canView('DON_HANG'),
      customers: canView('KHACH_HANG'),
      warehouses: canView('KHO_HANG'),
      reports: canView('BAO_CAO'),
      settings: hasPermission('CAU_HINH_HE_THONG')
    };

    return modulePermissions[module] || false;
  };

  // Get user's accessible menu items
  const getAccessibleMenuItems = () => {
    const menuItems = [];

    // Dashboard - always accessible
    menuItems.push({
      key: 'dashboard',
      label: 'Tổng quan',
      icon: 'DashboardOutlined',
      path: '/dashboard'
    });

    // Products with submenu
    if (canAccessModule('products')) {
      const productSubItems = [];

      // Main products list
      productSubItems.push({
        key: 'products-list',
        label: 'Danh sách sản phẩm',
        path: '/products'
      });

      // Product categories
      if (canView('LOAI_SAN_PHAM')) {
        productSubItems.push({
          key: 'product-categories',
          label: 'Loại sản phẩm',
          path: '/products/categories'
        });
      }

      // Product brands
      if (canView('NHAN_HIEU')) {
        productSubItems.push({
          key: 'product-brands',
          label: 'Nhãn hiệu',
          path: '/products/brands'
        });
      }

      // Product attributes
      if (canView('THUOC_TINH')) {
        productSubItems.push({
          key: 'product-attributes',
          label: 'Thuộc tính',
          path: '/products/attributes'
        });
      }

      // Product variants
      if (canView('PHIEN_BAN_SAN_PHAM')) {
        productSubItems.push({
          key: 'product-variants',
          label: 'Phiên bản sản phẩm',
          path: '/products/variants'
        });
      }

      menuItems.push({
        key: 'products',
        label: 'Sản phẩm',
        icon: 'ShoppingOutlined',
        children: productSubItems
      });
    }

    // Orders
    if (canAccessModule('orders')) {
      menuItems.push({
        key: 'orders',
        label: 'Đơn hàng',
        icon: 'ShoppingCartOutlined',
        path: '/orders'
      });
    }

    // Customers
    if (canAccessModule('customers')) {
      const customerSubItems = [];

      // Main customers list
      customerSubItems.push({
        key: 'customers-list',
        label: 'Danh sách khách hàng',
        path: '/customers'
      });

      // Customer groups
      if (canView('NHOM_KHACH_HANG')) {
        customerSubItems.push({
          key: 'customer-groups',
          label: 'Nhóm khách hàng',
          path: '/customers/groups'
        });
      }
      if (canView('KHACH_HANG')) {
        customerSubItems.push({
          key: 'customer-ctv',
          label: 'Cộng tác viên',
          path: '/customers/cong-tac-vien'
        });
      }
      menuItems.push({
        key: 'customers',
        label: 'Khách hàng',
        icon: 'UserOutlined',
        children: customerSubItems
      });
    }

    // Warehouses with submenu
    if (canAccessModule('warehouses')) {
      const warehouseSubItems = [];

      // Main warehouses list
      warehouseSubItems.push({
        key: 'warehouses-list',
        label: 'Danh sách kho hàng',
        path: '/warehouses'
      });

      // Inventory management
      if (canView('TON_KHO')) {
        warehouseSubItems.push({
          key: 'inventory',
          label: 'Quản lý tồn kho',
          path: '/warehouses/inventory'
        });
      }

      // Stock movements
      if (canView('XUAT_NHAP_KHO')) {
        warehouseSubItems.push({
          key: 'stock-movements',
          label: 'Xuất nhập kho',
          path: '/warehouses/movements'
        });
      }

      // Stock check
      if (canView('KIEM_KE_KHO')) {
        warehouseSubItems.push({
          key: 'stock-check',
          label: 'Kiểm kê kho',
          path: '/warehouses/check'
        });
      }

      menuItems.push({
        key: 'warehouses',
        label: 'Kho hàng',
        icon: 'HomeOutlined',
        children: warehouseSubItems
      });
    }

    // Reports
    if (canAccessModule('reports')) {
      menuItems.push({
        key: 'reports',
        label: 'Báo cáo',
        icon: 'BarChartOutlined',
        path: '/reports'
      });
    }

    // Accounting
    if (canAccessModule('accounting')) {
      const accountingSubItems = [];

      if (hasPermission('XEM_CONG_NO')) {
        accountingSubItems.push({
          key: 'debt-management',
          label: 'Quản lý công nợ',
          path: '/accounting/debt'
        });
      }

      if (hasPermission('XEM_BAO_CAO_CONG_NO')) {
        accountingSubItems.push({
          key: 'debt-report',
          label: 'Báo cáo công nợ',
          path: '/accounting/debt-report'
        });
      }

      if (accountingSubItems.length > 0) {
        menuItems.push({
          key: 'accounting',
          label: 'Kế toán',
          icon: 'DollarOutlined',
          children: accountingSubItems
        });
      }
    }

    // System Management (for admin/manager)
    if (isAdmin() || isManager()) {
      const systemItems = [];

      if (canAccessModule('users')) {
        systemItems.push({
          key: 'users',
          label: 'Người dùng',
          path: '/users'
        });
      }

      if (canAccessModule('roles')) {
        systemItems.push({
          key: 'roles',
          label: 'Vai trò',
          path: '/roles'
        });
      }

      if (canAccessModule('permissions')) {
        systemItems.push({
          key: 'permissions',
          label: 'Quyền',
          path: '/permissions'
        });
      }

      if (systemItems.length > 0) {
        menuItems.push({
          key: 'system',
          label: 'Hệ thống',
          icon: 'SettingOutlined',
          children: systemItems
        });
      }
    }

    return menuItems;
  };

  const value = {
    // Basic permission checks
    hasPermission,
    hasAnyPermission,
    hasRole,

    // User type checks
    isAdmin,
    isManager,
    isStaff,
    isCustomer,

    // Permission groups
    permissionGroups,
    hasPermissionGroup,

    // CRUD permission checks
    canView,
    canCreate,
    canEdit,
    canDelete,

    // Module access checks
    canAccessModule,
    getAccessibleMenuItems,

    // User data
    user,
    permissions
  };

  return (
    <PermissionContext.Provider value={value}>
      {children}
    </PermissionContext.Provider>
  );
};

// Custom hook to use permission context
export const usePermissions = () => {
  const context = useContext(PermissionContext);
  if (!context) {
    throw new Error('usePermissions must be used within a PermissionProvider');
  }
  return context;
};

export default PermissionContext;
