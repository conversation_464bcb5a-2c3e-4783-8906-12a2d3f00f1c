'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('vai_tro', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      ma_vai_tro: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      ten_vai_tro: {
        type: Sequelize.STRING,
        allowNull: false
      },
      mo_ta: {
        type: Sequelize.TEXT,
        allowNull: true
      }
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('vai_tro');
  }
};
