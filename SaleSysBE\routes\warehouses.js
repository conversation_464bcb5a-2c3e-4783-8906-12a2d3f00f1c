const express = require('express');
const { asyncHand<PERSON>, AppError } = require('../middleware/errorHandler');
const { requirePermission } = require('../middleware/auth');

const router = express.Router();

// Debug middleware
const debugAuth = (req, res, next) => {
  console.log('🔍 Warehouse API Debug:');
  console.log('- Headers:', req.headers.authorization ? 'Token present' : 'No token');
  console.log('- User:', req.user);
  console.log('- UserID:', req.userId);
  console.log('- Permissions:', req.userPermissions);
  next();
};

const {
  getWarehouses,
  getWarehouse,
  createWarehouse,
  updateWarehouse,
  deleteWarehouse,
  getInventory,
  adjustInventory,
  getStockMovements,
  createStockMovement,
  getProductStockHistory,
  getVariantStockHistory
} = require('../controllers/warehouseController');

const {
  getStockChecks,
  getStockCheck,
  createStockCheck,
  updateStockCheck,
  deleteStockCheck,
  getAvailableProducts,
  addProductToStockCheck,
  removeProductFromStockCheck,
  getStockCheckDetails,
  updateStockCheckItem,
  completeStockCheck
} = require('../controllers/stockCheckController');

// Inventory management routes (must be before /:id routes)
/**
 * @route GET /api/warehouses/inventory
 * @desc Lấy danh sách tồn kho
 * @access Private (Cần quyền XEM_TON_KHO)
 */
router.get('/inventory', requirePermission('XEM_TON_KHO'), asyncHandler(getInventory));

/**
 * @route POST /api/warehouses/inventory/adjust
 * @desc Điều chỉnh tồn kho
 * @access Private (Cần quyền SUA_TON_KHO)
 */
router.post('/inventory/adjust', requirePermission('SUA_TON_KHO'), asyncHandler(adjustInventory));

/**
 * @route POST /api/warehouses/inventory/test-adjust
 * @desc Test điều chỉnh tồn kho (debug)
 * @access Private
 */
router.post('/inventory/test-adjust', asyncHandler(async (req, res) => {
  console.log('🧪 TEST ADJUST called with:', req.body);
  res.json({
    success: true,
    message: 'Test endpoint working',
    received_data: req.body,
    user: req.user
  });
}));

/**
 * @route GET /api/warehouses/movements
 * @desc Lấy lịch sử xuất nhập kho
 * @access Private (Cần quyền XEM_KHO_HANG)
 */
router.get('/movements', requirePermission('XEM_KHO_HANG'), asyncHandler(getStockMovements));

/**
 * @route POST /api/warehouses/movements
 * @desc Tạo giao dịch xuất nhập kho
 * @access Private (Cần quyền SUA_KHO_HANG)
 */
router.post('/movements', requirePermission('SUA_KHO_HANG'), asyncHandler(createStockMovement));

/**
 * @route GET /api/warehouses/products/:productId/history
 * @desc Lấy lịch sử kho theo sản phẩm
 * @access Private (Cần quyền XEM_KHO_HANG)
 */
router.get('/products/:productId/history', requirePermission('XEM_KHO_HANG'), asyncHandler(getProductStockHistory));

/**
 * @route GET /api/warehouses/variants/:variantId/history
 * @desc Lấy lịch sử kho theo phiên bản sản phẩm
 * @access Private (Cần quyền XEM_KHO_HANG)
 */
router.get('/variants/:variantId/history', requirePermission('XEM_KHO_HANG'), asyncHandler(getVariantStockHistory));

// Stock Check routes
/**
 * @route GET /api/warehouses/stock-checks
 * @desc Lấy danh sách phiếu kiểm kê
 * @access Private (Cần quyền XEM_KHO_HANG)
 */
router.get('/stock-checks', requirePermission('XEM_KHO_HANG'), asyncHandler(getStockChecks));

/**
 * @route GET /api/warehouses/stock-checks/:id
 * @desc Lấy chi tiết phiếu kiểm kê
 * @access Private (Cần quyền XEM_KHO_HANG)
 */
router.get('/stock-checks/:id', requirePermission('XEM_KHO_HANG'), asyncHandler(getStockCheck));

/**
 * @route POST /api/warehouses/stock-checks
 * @desc Tạo phiếu kiểm kê mới
 * @access Private (Cần quyền SUA_KHO_HANG)
 */
router.post('/stock-checks', requirePermission('SUA_KHO_HANG'), asyncHandler(createStockCheck));

/**
 * @route PUT /api/warehouses/stock-checks/:id
 * @desc Cập nhật phiếu kiểm kê
 * @access Private (Cần quyền SUA_KHO_HANG)
 */
router.put('/stock-checks/:id', requirePermission('SUA_KHO_HANG'), asyncHandler(updateStockCheck));

/**
 * @route DELETE /api/warehouses/stock-checks/:id
 * @desc Xóa phiếu kiểm kê
 * @access Private (Cần quyền SUA_KHO_HANG)
 */
router.delete('/stock-checks/:id', requirePermission('SUA_KHO_HANG'), asyncHandler(deleteStockCheck));

/**
 * @route GET /api/warehouses/stock-checks/:id/details
 * @desc Lấy chi tiết sản phẩm cần kiểm kê
 * @access Private (Cần quyền XEM_KHO_HANG)
 */
router.get('/stock-checks/:id/details', requirePermission('XEM_KHO_HANG'), asyncHandler(getStockCheckDetails));

/**
 * @route PUT /api/warehouses/stock-checks/:id/items/:itemId
 * @desc Cập nhật số lượng thực tế cho sản phẩm
 * @access Private (Cần quyền SUA_KHO_HANG)
 */
router.put('/stock-checks/:id/items/:itemId', requirePermission('SUA_KHO_HANG'), asyncHandler(updateStockCheckItem));

/**
 * @route POST /api/warehouses/stock-checks/:id/complete
 * @desc Hoàn thành kiểm kê
 * @access Private (Cần quyền SUA_KHO_HANG)
 */
router.post('/stock-checks/:id/complete', requirePermission('SUA_KHO_HANG'), asyncHandler(completeStockCheck));

/**
 * @route GET /api/warehouses/stock-checks/:id/available-products
 * @desc Lấy danh sách sản phẩm có thể thêm vào kiểm kê
 * @access Private (Cần quyền XEM_KHO_HANG)
 */
router.get('/stock-checks/:id/available-products', requirePermission('XEM_KHO_HANG'), asyncHandler(getAvailableProducts));

/**
 * @route POST /api/warehouses/stock-checks/:id/products
 * @desc Thêm sản phẩm vào phiếu kiểm kê
 * @access Private (Cần quyền SUA_KHO_HANG)
 */
router.post('/stock-checks/:id/products', requirePermission('SUA_KHO_HANG'), asyncHandler(addProductToStockCheck));

/**
 * @route DELETE /api/warehouses/stock-checks/:id/items/:itemId
 * @desc Xóa sản phẩm khỏi phiếu kiểm kê
 * @access Private (Cần quyền SUA_KHO_HANG)
 */
router.delete('/stock-checks/:id/items/:itemId', requirePermission('SUA_KHO_HANG'), asyncHandler(removeProductFromStockCheck));

// Warehouse CRUD routes
/**
 * @route GET /api/warehouses
 * @desc Lấy danh sách kho hàng
 * @access Private (Cần quyền XEM_KHO_HANG)
 */
router.get('/', debugAuth, requirePermission('XEM_KHO_HANG'), asyncHandler(getWarehouses));

/**
 * @route GET /api/warehouses/:id
 * @desc Lấy thông tin chi tiết kho hàng
 * @access Private (Cần quyền XEM_KHO_HANG)
 */
router.get('/:id', requirePermission('XEM_KHO_HANG'), asyncHandler(getWarehouse));

/**
 * @route POST /api/warehouses
 * @desc Tạo kho hàng mới
 * @access Private (Cần quyền THEM_KHO_HANG)
 */
router.post('/', requirePermission('THEM_KHO_HANG'), asyncHandler(createWarehouse));

/**
 * @route PUT /api/warehouses/:id
 * @desc Cập nhật kho hàng
 * @access Private (Cần quyền SUA_KHO_HANG)
 */
router.put('/:id', requirePermission('SUA_KHO_HANG'), asyncHandler(updateWarehouse));

/**
 * @route DELETE /api/warehouses/:id
 * @desc Xóa kho hàng
 * @access Private (Cần quyền XOA_KHO_HANG)
 */
router.delete('/:id', requirePermission('XOA_KHO_HANG'), asyncHandler(deleteWarehouse));

module.exports = router;
