import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![usergroup-delete](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4OCA3ODRINjY0Yy00LjQgMC04IDMuNi04IDh2NDhjMCA0LjQgMy42IDggOCA4aDIyNGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHpNMzczLjUgNTEwLjRjLS45LTguNy0xLjQtMTcuNS0xLjQtMjYuNCAwLTE1LjkgMS41LTMxLjQgNC4zLTQ2LjUuNy0zLjYtMS4yLTcuMy00LjUtOC44LTEzLjYtNi4xLTI2LjEtMTQuNS0zNi45LTI1LjFhMTI3LjU0IDEyNy41NCAwIDAxLTM4LjctOTUuNGMuOS0zMi4xIDEzLjgtNjIuNiAzNi4zLTg1LjYgMjQuNy0yNS4zIDU3LjktMzkuMSA5My4yLTM4LjcgMzEuOS4zIDYyLjcgMTIuNiA4NiAzNC40IDcuOSA3LjQgMTQuNyAxNS42IDIwLjQgMjQuNCAyIDMuMSA1LjkgNC40IDkuMyAzLjIgMTcuNi02LjEgMzYuMi0xMC40IDU1LjMtMTIuNCA1LjYtLjYgOC44LTYuNiA2LjMtMTEuNi0zMi41LTY0LjMtOTguOS0xMDguNy0xNzUuNy0xMDkuOS0xMTAuOS0xLjctMjAzLjMgODkuMi0yMDMuMyAxOTkuOSAwIDYyLjggMjguOSAxMTguOCA3NC4yIDE1NS41LTMxLjggMTQuNy02MS4xIDM1LTg2LjUgNjAuNC01NC44IDU0LjctODUuOCAxMjYuOS04Ny44IDIwNGE4IDggMCAwMDggOC4yaDU2LjFjNC4zIDAgNy45LTMuNCA4LTcuNyAxLjktNTggMjUuNC0xMTIuMyA2Ni43LTE1My41IDI5LjQtMjkuNCA2NS40LTQ5LjggMTA0LjctNTkuNyAzLjktMSA2LjUtNC43IDYtOC43ek04MjQgNDg0YzAtMTA5LjQtODcuOS0xOTguMy0xOTYuOS0yMDBDNTE2LjMgMjgyLjMgNDI0IDM3My4yIDQyNCA0ODRjMCA2Mi44IDI5IDExOC44IDc0LjIgMTU1LjVhMzAwLjk1IDMwMC45NSAwIDAwLTg2LjQgNjAuNEMzNTcgNzU0LjYgMzI2IDgyNi44IDMyNCA5MDMuOGE4IDggMCAwMDggOC4yaDU2YzQuMyAwIDcuOS0zLjQgOC03LjcgMS45LTU4IDI1LjQtMTEyLjMgNjYuNy0xNTMuNUM1MDUuOCA3MDcuNyA1NjMgNjg0IDYyNCA2ODRjMTEwLjQgMCAyMDAtODkuNSAyMDAtMjAwem0tMTA5LjUgOTAuNUM2OTAuMyA1OTguNyA2NTguMiA2MTIgNjI0IDYxMnMtNjYuMy0xMy4zLTkwLjUtMzcuNWExMjcuMjYgMTI3LjI2IDAgMDEtMzcuNS05MS44Yy4zLTMyLjggMTMuNC02NC41IDM2LjMtODggMjQtMjQuNiA1Ni4xLTM4LjMgOTAuNC0zOC43IDMzLjktLjMgNjYuOCAxMi45IDkxIDM2LjYgMjQuOCAyNC4zIDM4LjQgNTYuOCAzOC40IDkxLjQtLjEgMzQuMi0xMy40IDY2LjMtMzcuNiA5MC41eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
