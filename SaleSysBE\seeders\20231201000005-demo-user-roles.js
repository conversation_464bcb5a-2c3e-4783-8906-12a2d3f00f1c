'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // L<PERSON>y danh sách người dùng và vai trò
    const users = await queryInterface.sequelize.query(
      'SELECT id, email FROM nguoi_dung',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const roles = await queryInterface.sequelize.query(
      'SELECT id, ma_vai_tro FROM vai_tro',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    const userRoles = [];

    // Gán vai trò cho từng user
    const adminUser = users.find(u => u.email === '<EMAIL>');
    const adminRole = roles.find(r => r.ma_vai_tro === 'ADMIN');
    if (adminUser && adminRole) {
      userRoles.push({
        nguoi_dung_id: adminUser.id,
        vai_tro_id: adminRole.id
      });
    }

    const managerUser = users.find(u => u.email === '<EMAIL>');
    const managerRole = roles.find(r => r.ma_vai_tro === 'QUAN_LY');
    if (managerUser && managerRole) {
      userRoles.push({
        nguoi_dung_id: managerUser.id,
        vai_tro_id: managerRole.id
      });
    }

    const salesUser = users.find(u => u.email === '<EMAIL>');
    const salesRole = roles.find(r => r.ma_vai_tro === 'NHAN_VIEN_BAN_HANG');
    if (salesUser && salesRole) {
      userRoles.push({
        nguoi_dung_id: salesUser.id,
        vai_tro_id: salesRole.id
      });
    }

    const warehouseUser = users.find(u => u.email === '<EMAIL>');
    const warehouseRole = roles.find(r => r.ma_vai_tro === 'NHAN_VIEN_KHO');
    if (warehouseUser && warehouseRole) {
      userRoles.push({
        nguoi_dung_id: warehouseUser.id,
        vai_tro_id: warehouseRole.id
      });
    }

    const accountantUser = users.find(u => u.email === '<EMAIL>');
    const accountantRole = roles.find(r => r.ma_vai_tro === 'KE_TOAN');
    if (accountantUser && accountantRole) {
      userRoles.push({
        nguoi_dung_id: accountantUser.id,
        vai_tro_id: accountantRole.id
      });
    }

    if (userRoles.length > 0) {
      await queryInterface.bulkInsert('nguoi_dung_vai_tro', userRoles, {});
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('nguoi_dung_vai_tro', null, {});
  }
};
