import { useQuery, useMutation, useQueryClient } from 'react-query';
import { message } from 'antd';
import { ordersAPI } from '../services/api';

// Query keys
const ORDER_QUERY_KEYS = {
  orders: () => ['orders'],
  order: (id) => ['orders', id],
  orderDetails: (id) => ['orders', id, 'details'],
};

// Get orders list
export const useOrders = (params = {}) => {
  return useQuery(
    [...ORDER_QUERY_KEYS.orders(), params],
    () => {
      // Tạm thời sử dụng test API không cần auth
      // Filter out undefined values
      const cleanParams = Object.fromEntries(
        Object.entries(params).filter(([_, value]) => value !== undefined && value !== 'undefined' && value !== '')
      );
      const testParams = new URLSearchParams(cleanParams).toString();
      const url = `http://localhost:5000/api/test/orders${testParams ? '?' + testParams : ''}`;
      console.log('📋 Orders API URL:', url);
      return fetch(url).then(res => res.json());
    },
    {
      select: (response) => {
        console.log('📋 Orders API response:', response);
        // API test trả về trực tiếp data với field mapping mới
        if (response?.success && response?.data) {
          return {
            ...response,
            data: response.data.map(order => ({
              ...order,
              // Map field names for compatibility with frontend
              ngay_dat_hang: order.ngay_ban,
              tong_tien_hang: order.tong_tien,
              giam_gia: order.chiet_khau,
              tong_thanh_toan: order.tong_phai_tra
            })),
            // Map stats field names
            stats: response.stats ? {
              total_orders: response.stats.total_orders,
              pending_orders: response.stats.pending_orders,
              completed_orders: response.stats.completed_orders,
              total_revenue: response.stats.total_revenue
            } : {}
          };
        }
        return response;
      },
      staleTime: 30 * 1000, // 30 seconds
    }
  );
};

// Get single order
export const useOrder = (id) => {
  return useQuery(
    ORDER_QUERY_KEYS.order(id),
    () => ordersAPI.getOrder(id),
    {
      enabled: !!id,
      select: (data) => data.data,
      staleTime: 30 * 1000,
    }
  );
};

// Get order details
export const useOrderDetails = (id) => {
  return useQuery(
    ORDER_QUERY_KEYS.orderDetails(id),
    () => ordersAPI.getOrderDetails(id),
    {
      enabled: !!id,
      select: (data) => data.data,
      staleTime: 30 * 1000,
    }
  );
};

// Create order mutation
export const useCreateOrder = () => {
  const queryClient = useQueryClient();

  return useMutation(
    (orderData) => {
      // Tạm thời sử dụng test API không cần auth
      console.log('📋 Sending order data to API:', orderData);
      return fetch('http://localhost:5000/api/test/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(orderData)
      }).then(res => {
        if (!res.ok) {
          throw new Error(`HTTP ${res.status}: ${res.statusText}`);
        }
        return res.json();
      });
    },
    {
      onSuccess: (data) => {
        console.log('📋 Order creation success:', data);
        if (data?.success) {
          message.success(`Tạo đơn hàng thành công! Mã đơn: ${data.data?.ma_don_hang}`);
        } else {
          message.success('Tạo đơn hàng thành công!');
        }
        queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
        // Invalidate inventory to update stock
        queryClient.invalidateQueries(['inventory']);
        queryClient.invalidateQueries(['product-variants']);
      },
      onError: (error) => {
        console.error('📋 Order creation error:', error);
        message.error(error.message || 'Có lỗi xảy ra khi tạo đơn hàng');
      },
    }
  );
};

// Update order mutation
export const useUpdateOrder = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, ...data }) => ordersAPI.updateOrder(id, data),
    {
      onSuccess: (data, variables) => {
        message.success('Cập nhật đơn hàng thành công!');
        queryClient.invalidateQueries(ORDER_QUERY_KEYS.order(variables.id));
        queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật đơn hàng');
      },
    }
  );
};

// Delete order mutation
export const useDeleteOrder = () => {
  const queryClient = useQueryClient();

  return useMutation(ordersAPI.deleteOrder, {
    onSuccess: () => {
      message.success('Xóa đơn hàng thành công!');
      queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa đơn hàng');
    },
  });
};

// Update order status mutation
export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, status }) => ordersAPI.updateOrderStatus(id, status),
    {
      onSuccess: (data, variables) => {
        message.success('Cập nhật trạng thái đơn hàng thành công!');
        queryClient.invalidateQueries(ORDER_QUERY_KEYS.order(variables.id));
        queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
        // Invalidate inventory if order is completed/cancelled
        if (['hoan_thanh', 'huy'].includes(variables.status)) {
          queryClient.invalidateQueries(['inventory']);
        }
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật trạng thái');
      },
    }
  );
};

// Cancel order mutation
export const useCancelOrder = () => {
  const queryClient = useQueryClient();

  return useMutation(ordersAPI.cancelOrder, {
    onSuccess: (data, id) => {
      message.success('Hủy đơn hàng thành công!');
      queryClient.invalidateQueries(ORDER_QUERY_KEYS.order(id));
      queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
      // Invalidate inventory to restore stock
      queryClient.invalidateQueries(['inventory']);
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi hủy đơn hàng');
    },
  });
};

// Complete order mutation
export const useCompleteOrder = () => {
  const queryClient = useQueryClient();

  return useMutation(ordersAPI.completeOrder, {
    onSuccess: (data, id) => {
      message.success('Hoàn thành đơn hàng thành công!');
      queryClient.invalidateQueries(ORDER_QUERY_KEYS.order(id));
      queryClient.invalidateQueries(ORDER_QUERY_KEYS.orders());
      // Invalidate inventory to update stock
      queryClient.invalidateQueries(['inventory']);
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi hoàn thành đơn hàng');
    },
  });
};

export { ORDER_QUERY_KEYS };
