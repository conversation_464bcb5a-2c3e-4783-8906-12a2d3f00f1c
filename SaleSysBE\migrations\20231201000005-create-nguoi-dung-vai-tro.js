'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('nguoi_dung_vai_tro', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      nguoi_dung_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      vai_tro_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'vai_tro',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      }
    });

    // Check if index already exists before creating
    const indexes = await queryInterface.showIndex('nguoi_dung_vai_tro');
    const indexExists = indexes.some(index => index.name === 'nguoi_dung_vai_tro_unique');

    if (!indexExists) {
      await queryInterface.addIndex('nguoi_dung_vai_tro', ['nguoi_dung_id', 'vai_tro_id'], {
        unique: true,
        name: 'nguoi_dung_vai_tro_unique'
      });
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('nguoi_dung_vai_tro');
  }
};
