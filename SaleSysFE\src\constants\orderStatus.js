// Order status constants matching backend ENUM
export const ORDER_STATUS = {
  CHO_XU_LY: 'cho_xu_ly',
  DA_XAC_NHAN: 'da_xac_nhan', 
  DA_DONG_GOI: 'da_dong_goi',
  DA_GIAO: 'da_giao',
  HOAN_THANH: 'hoan_thanh',
  HUY: 'huy'
};

export const ORDER_STATUS_LABELS = {
  [ORDER_STATUS.CHO_XU_LY]: 'Chờ xử lý',
  [ORDER_STATUS.DA_XAC_NHAN]: 'Đã xác nhận',
  [ORDER_STATUS.DA_DONG_GOI]: 'Đã đóng gói', 
  [ORDER_STATUS.DA_GIAO]: 'Đã giao',
  [ORDER_STATUS.HOAN_THANH]: 'Hoàn thành',
  [ORDER_STATUS.HUY]: 'Hủy'
};

export const ORDER_STATUS_COLORS = {
  [ORDER_STATUS.CHO_XU_LY]: 'orange',
  [ORDER_STATUS.DA_XAC_NHAN]: 'blue',
  [ORDER_STATUS.DA_DONG_GOI]: 'cyan',
  [ORDER_STATUS.DA_GIAO]: 'purple', 
  [ORDER_STATUS.HOAN_THANH]: 'green',
  [ORDER_STATUS.HUY]: 'red'
};

// Delivery methods
export const DELIVERY_METHODS = {
  STORE_PICKUP: 'store_pickup',
  DELIVERY: 'delivery',
  CUSTOMER_PICKUP: 'customer_pickup', 
  LATER_DELIVERY: 'later_delivery'
};

export const DELIVERY_METHOD_LABELS = {
  [DELIVERY_METHODS.STORE_PICKUP]: 'Đây qua hàng vận chuyển',
  [DELIVERY_METHODS.DELIVERY]: 'Đây vận chuyển ngoài',
  [DELIVERY_METHODS.CUSTOMER_PICKUP]: 'Khách nhận tại cửa hàng',
  [DELIVERY_METHODS.LATER_DELIVERY]: 'Giao hàng sau'
};

// Discount types
export const DISCOUNT_TYPES = {
  AMOUNT: 'amount',
  PERCENT: 'percent'
};

export const DISCOUNT_TYPE_LABELS = {
  [DISCOUNT_TYPES.AMOUNT]: 'Giảm theo số tiền',
  [DISCOUNT_TYPES.PERCENT]: 'Giảm theo phần trăm'
};
