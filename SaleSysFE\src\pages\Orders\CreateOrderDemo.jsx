import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  Button,
  Form,
  Typography,
  Space,
  Divider,
  Table,
  InputNumber,
  AutoComplete,
  Checkbox,
  message,
  Empty,
  Tooltip
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  DeleteOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  DollarOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const CreateOrderDemo = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  // States
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [customerSearch, setCustomerSearch] = useState('');
  const [productSearch, setProductSearch] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [discountType, setDiscountType] = useState('amount'); // 'amount' or 'percent'
  const [isStockCheck, setIsStockCheck] = useState(true);
  const [customerDropdownOpen, setCustomerDropdownOpen] = useState(false);
  const [productDropdownOpen, setProductDropdownOpen] = useState(false);

  // Mock data
  const mockCustomers = [
    { id: 1, ten: 'Nguyễn Văn A', so_dien_thoai: '0123456789', ma_khach_hang: 'KH001', dia_chi: 'Số 123, Đường ABC, Quận Ba Đình, Hà Nội', email: '<EMAIL>' },
    { id: 2, ten: 'Trần Thị B', so_dien_thoai: '0987654321', ma_khach_hang: 'KH002', dia_chi: 'Số 456, Đường XYZ, Quận 1, TP.HCM', email: '<EMAIL>' },
    { id: 3, ten: 'Lê Văn C', so_dien_thoai: '0369852147', ma_khach_hang: 'KH003', dia_chi: 'Số 789, Đường DEF, Quận Hải Châu, Đà Nẵng', email: '<EMAIL>' },
    { id: 4, ten: 'Phạm Thị D', so_dien_thoai: '0912345678', ma_khach_hang: 'KH004', dia_chi: 'Số 321, Đường GHI, Quận Cầu Giấy, Hà Nội', email: '<EMAIL>' },
    { id: 5, ten: 'Hoàng Văn E', so_dien_thoai: '0876543210', ma_khach_hang: 'KH005', dia_chi: 'Số 654, Đường JKL, Quận 3, TP.HCM', email: '<EMAIL>' },
    { id: 6, ten: 'Vũ Thị F', so_dien_thoai: '0945678123', ma_khach_hang: 'KH006', dia_chi: 'Số 987, Đường MNO, Quận Thanh Khê, Đà Nẵng', email: '<EMAIL>' }
  ];

  const mockProducts = [
    {
      id: 1,
      ten_san_pham: 'iPhone 15 Pro',
      ten_phien_ban: '256GB - Titan Tự Nhiên',
      ma_sku: 'IP15P-256-TN',
      anh: 'https://cdn.tgdd.vn/Products/Images/42/305658/iphone-15-pro-max-blue-thumbnew-600x600.jpg',
      gia_ban_le: 29990000,
      ton_kho: 15
    },
    {
      id: 2,
      ten_san_pham: 'Samsung Galaxy S24',
      ten_phien_ban: '128GB - Đen',
      ma_sku: 'SGS24-128-BK',
      anh: 'https://cdn.tgdd.vn/Products/Images/42/307174/samsung-galaxy-s24-violet-thumbnew-600x600.jpg',
      gia_ban_le: 22990000,
      ton_kho: 8
    },
    {
      id: 3,
      ten_san_pham: 'MacBook Air M3',
      ten_phien_ban: '13 inch - 8GB/256GB',
      ma_sku: 'MBA-M3-8-256',
      anh: 'https://cdn.tgdd.vn/Products/Images/44/322096/macbook-air-13-inch-m3-2024-starlight-thumbnew-600x600.jpg',
      gia_ban_le: 32990000,
      ton_kho: 5
    }
  ];

  // Calculate totals
  const subtotal = selectedProducts.reduce((sum, item) => 
    sum + (item.so_luong * item.gia_ban), 0
  );
  
  const discountAmount = form.getFieldValue('giam_gia') || 0;
  const finalDiscount = discountType === 'percent' 
    ? (subtotal * discountAmount / 100)
    : discountAmount;
  
  const total = subtotal - finalDiscount;

  // Handle customer selection
  const handleCustomerSelect = (value, option) => {
    const customer = mockCustomers.find(c => c.id === value);
    setSelectedCustomer(customer);
    setCustomerSearch(customer?.ten || '');
    setCustomerDropdownOpen(false);
    form.setFieldsValue({
      khach_hang_id: customer?.id,
      ten_khach_hang: customer?.ten,
      so_dien_thoai: customer?.so_dien_thoai,
      dia_chi: customer?.dia_chi
    });
  };

  // Handle customer search change
  const handleCustomerSearchChange = (value) => {
    setCustomerSearch(value);
    if (!customerDropdownOpen) {
      setCustomerDropdownOpen(true);
    }
  };

  // Handle customer dropdown focus
  const handleCustomerFocus = () => {
    setCustomerDropdownOpen(true);
  };

  // Handle customer dropdown blur
  const handleCustomerBlur = () => {
    // Delay hiding dropdown to allow selection
    setTimeout(() => {
      setCustomerDropdownOpen(false);
    }, 200);
  };

  // Handle product selection
  const handleProductSelect = (productId) => {
    const product = mockProducts.find(p => p.id === productId);
    if (!product) return;

    // Check if product already exists
    const existingIndex = selectedProducts.findIndex(p => p.id === productId);
    if (existingIndex >= 0) {
      // Increase quantity
      const newProducts = [...selectedProducts];
      newProducts[existingIndex].so_luong += 1;
      setSelectedProducts(newProducts);
    } else {
      // Add new product
      const newProduct = {
        id: product.id,
        ten_san_pham: product.ten_san_pham,
        ten_phien_ban: product.ten_phien_ban,
        ma_sku: product.ma_sku,
        anh: product.anh,
        gia_ban: product.gia_ban_le || 0,
        so_luong: 1,
        ton_kho: product.ton_kho || 0
      };
      setSelectedProducts([...selectedProducts, newProduct]);
    }
    setProductSearch('');
    setProductDropdownOpen(false);
  };

  // Handle product search change
  const handleProductSearchChange = (value) => {
    setProductSearch(value);
    if (!productDropdownOpen) {
      setProductDropdownOpen(true);
    }
  };

  // Handle product dropdown focus
  const handleProductFocus = () => {
    setProductDropdownOpen(true);
  };

  // Handle product dropdown blur
  const handleProductBlur = () => {
    // Delay hiding dropdown to allow selection
    setTimeout(() => {
      setProductDropdownOpen(false);
    }, 200);
  };

  // Handle quantity change
  const handleQuantityChange = (productId, quantity) => {
    const newProducts = selectedProducts.map(product => 
      product.id === productId 
        ? { ...product, so_luong: quantity || 0 }
        : product
    );
    setSelectedProducts(newProducts);
  };

  // Handle price change
  const handlePriceChange = (productId, price) => {
    const newProducts = selectedProducts.map(product => 
      product.id === productId 
        ? { ...product, gia_ban: price || 0 }
        : product
    );
    setSelectedProducts(newProducts);
  };

  // Remove product
  const handleRemoveProduct = (productId) => {
    setSelectedProducts(selectedProducts.filter(p => p.id !== productId));
  };

  // Handle form submit
  const handleSubmit = async (values) => {
    try {
      if (selectedProducts.length === 0) {
        message.error('Vui lòng chọn ít nhất một sản phẩm');
        return;
      }

      const orderData = {
        ...values,
        khach_hang_id: selectedCustomer?.id,
        chi_tiet_don_hang: selectedProducts.map(product => ({
          phien_ban_san_pham_id: product.id,
          so_luong: product.so_luong,
          gia_ban: product.gia_ban,
          thanh_tien: product.so_luong * product.gia_ban
        })),
        tong_tien_hang: subtotal,
        giam_gia: finalDiscount,
        tong_thanh_toan: total,
        loai_giam_gia: discountType
      };

      console.log('Order data:', orderData);
      message.success('Tạo đơn hàng thành công! (Demo)');
      // navigate('/orders');
    } catch (error) {
      message.error('Có lỗi xảy ra khi tạo đơn hàng');
    }
  };

  // Filter customers based on search
  const filteredCustomers = customerDropdownOpen
    ? mockCustomers.filter(customer =>
        customer.ten.toLowerCase().includes(customerSearch.toLowerCase()) ||
        customer.so_dien_thoai.includes(customerSearch) ||
        customer.ma_khach_hang.toLowerCase().includes(customerSearch.toLowerCase()) ||
        customer.email.toLowerCase().includes(customerSearch.toLowerCase())
      )
    : [];

  // Filter products based on search
  const filteredProducts = productDropdownOpen
    ? mockProducts.filter(product =>
        product.ten_san_pham.toLowerCase().includes(productSearch.toLowerCase()) ||
        product.ten_phien_ban.toLowerCase().includes(productSearch.toLowerCase()) ||
        product.ma_sku.toLowerCase().includes(productSearch.toLowerCase())
      )
    : [];

  // Product table columns
  const productColumns = [
    {
      title: 'STT',
      key: 'stt',
      width: 50,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Sản phẩm',
      key: 'product',
      width: 300,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <img 
            src={record.anh || '/placeholder-image.png'} 
            alt={record.ten_san_pham}
            style={{ width: 40, height: 40, objectFit: 'cover', borderRadius: 4 }}
          />
          <div>
            <div style={{ fontWeight: 500 }}>{record.ten_san_pham}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.ten_phien_ban} - {record.ma_sku}
            </Text>
            {isStockCheck && (
              <div>
                <Text type="secondary" style={{ fontSize: 11 }}>
                  Tồn: {record.ton_kho}
                </Text>
                {record.so_luong > record.ton_kho && (
                  <Text type="danger" style={{ fontSize: 11, marginLeft: 8 }}>
                    Vượt tồn kho!
                  </Text>
                )}
              </div>
            )}
          </div>
        </div>
      )
    },
    {
      title: 'Số lượng',
      key: 'so_luong',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <InputNumber
          min={1}
          value={record.so_luong}
          onChange={(value) => handleQuantityChange(record.id, value)}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Đơn giá',
      key: 'gia_ban',
      width: 120,
      align: 'right',
      render: (_, record) => (
        <InputNumber
          min={0}
          value={record.gia_ban}
          onChange={(value) => handlePriceChange(record.id, value)}
          formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
          parser={value => value.replace(/\$\s?|(,*)/g, '')}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: 'Thành tiền',
      key: 'thanh_tien',
      width: 120,
      align: 'right',
      render: (_, record) => (
        <Text strong>
          {(record.so_luong * record.gia_ban).toLocaleString('vi-VN')}đ
        </Text>
      )
    },
    {
      title: '',
      key: 'action',
      width: 50,
      align: 'center',
      render: (_, record) => (
        <Button
          type="text"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleRemoveProduct(record.id)}
          size="small"
        />
      )
    }
  ];

  return (
    <div style={{ padding: '24px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      {/* Header */}
      <div style={{
        marginBottom: '24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Title level={3} style={{ margin: 0 }}>
          <ShoppingCartOutlined style={{ marginRight: 8 }} />
          Tạo đơn hàng (Demo)
        </Title>
        <Space>
          <Button onClick={() => navigate('/orders')}>
            Hủy
          </Button>
          <Button 
            type="primary" 
            onClick={() => form.submit()}
            disabled={selectedProducts.length === 0}
          >
            Tạo đơn hàng
          </Button>
        </Space>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          ngay_dat_hang: dayjs(),
          trang_thai: 'cho_xu_ly',
          giam_gia: 0
        }}
      >
        <Row gutter={24}>
          {/* Left Column */}
          <Col span={16}>
            {/* Customer Information */}
            <Card 
              title={
                <Space>
                  <UserOutlined />
                  Thông tin khách hàng
                </Space>
              }
              style={{ marginBottom: 24 }}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="Tìm kiếm khách hàng">
                    <AutoComplete
                      value={customerSearch}
                      onChange={handleCustomerSearchChange}
                      onSelect={handleCustomerSelect}
                      onFocus={handleCustomerFocus}
                      onBlur={handleCustomerBlur}
                      open={customerDropdownOpen}
                      placeholder="Nhấn để chọn khách hàng hoặc gõ để tìm kiếm..."
                      options={filteredCustomers.map(customer => ({
                        value: customer.id,
                        label: (
                          <div style={{ padding: '8px 0' }}>
                            <div style={{ fontWeight: 500, marginBottom: 4 }}>
                              {customer.ten}
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Text type="secondary" style={{ fontSize: 12 }}>
                                {customer.so_dien_thoai} - {customer.ma_khach_hang}
                              </Text>
                            </div>
                            <Text type="secondary" style={{ fontSize: 11, color: '#999' }}>
                              {customer.dia_chi}
                            </Text>
                          </div>
                        )
                      }))}
                      filterOption={false}
                      dropdownStyle={{ maxHeight: 300, overflow: 'auto' }}
                      notFoundContent={
                        customerDropdownOpen && customerSearch ? (
                          <div style={{ padding: '12px', textAlign: 'center' }}>
                            <Text type="secondary" style={{ marginBottom: 8, display: 'block' }}>
                              Không tìm thấy khách hàng
                            </Text>
                            <Button
                              type="link"
                              size="small"
                              onClick={() => {
                                // Handle add new customer
                                message.info('Tính năng thêm khách hàng mới sẽ được phát triển');
                              }}
                            >
                              + Thêm khách hàng mới
                            </Button>
                          </div>
                        ) : customerDropdownOpen ? (
                          <div style={{ padding: '12px', textAlign: 'center' }}>
                            <Text type="secondary">Gõ để tìm kiếm khách hàng</Text>
                          </div>
                        ) : null
                      }
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="ten_khach_hang" label="Tên khách hàng">
                    <Input placeholder="Nhập tên khách hàng" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="so_dien_thoai" label="Số điện thoại">
                    <Input placeholder="Nhập số điện thoại" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="dia_chi" label="Địa chỉ">
                    <Input placeholder="Nhập địa chỉ" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Product Information */}
            <Card 
              title={
                <Space>
                  <ShoppingCartOutlined />
                  Thông tin sản phẩm
                </Space>
              }
              extra={
                <Space>
                  <Checkbox 
                    checked={isStockCheck}
                    onChange={(e) => setIsStockCheck(e.target.checked)}
                  >
                    Tích đúng tồn
                  </Checkbox>
                  <Text type="secondary">Kiểm tra tồn kho</Text>
                </Space>
              }
            >
              <div style={{ marginBottom: 16 }}>
                <AutoComplete
                  value={productSearch}
                  onChange={handleProductSearchChange}
                  onSelect={handleProductSelect}
                  onFocus={handleProductFocus}
                  onBlur={handleProductBlur}
                  open={productDropdownOpen}
                  placeholder="Nhấn để chọn sản phẩm hoặc gõ để tìm kiếm... (F3)"
                  style={{ width: '100%' }}
                  options={filteredProducts.map(product => ({
                    value: product.id,
                    label: (
                      <div style={{ display: 'flex', alignItems: 'center', gap: 12, padding: '8px 0' }}>
                        <img
                          src={product.anh || '/placeholder-image.png'}
                          alt={product.ten_san_pham}
                          style={{ width: 40, height: 40, objectFit: 'cover', borderRadius: 6 }}
                        />
                        <div style={{ flex: 1 }}>
                          <div style={{ fontWeight: 500, marginBottom: 2 }}>
                            {product.ten_san_pham}
                          </div>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            {product.ten_phien_ban} - {product.ma_sku}
                          </Text>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 4 }}>
                            <Text style={{ fontSize: 12, color: '#1890ff' }}>
                              {product.gia_ban_le?.toLocaleString('vi-VN')}đ
                            </Text>
                            <Text style={{ fontSize: 12, color: product.ton_kho > 0 ? '#52c41a' : '#ff4d4f' }}>
                              Tồn: {product.ton_kho || 0}
                            </Text>
                          </div>
                        </div>
                      </div>
                    )
                  }))}
                  filterOption={false}
                  dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
                  notFoundContent={
                    productDropdownOpen && productSearch ? (
                      <div style={{ padding: '12px', textAlign: 'center' }}>
                        <Text type="secondary">Không tìm thấy sản phẩm</Text>
                      </div>
                    ) : productDropdownOpen ? (
                      <div style={{ padding: '12px', textAlign: 'center' }}>
                        <Text type="secondary">Gõ để tìm kiếm sản phẩm</Text>
                      </div>
                    ) : null
                  }
                />
              </div>

              {selectedProducts.length === 0 ? (
                <Empty 
                  description="Chưa có thông tin sản phẩm"
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                >
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />}
                    onClick={() => document.querySelector('.ant-select-selection-search-input').focus()}
                  >
                    Thêm sản phẩm
                  </Button>
                </Empty>
              ) : (
                <Table
                  columns={productColumns}
                  dataSource={selectedProducts}
                  rowKey="id"
                  pagination={false}
                  size="small"
                  scroll={{ x: 800 }}
                />
              )}
            </Card>
          </Col>

          {/* Right Column */}
          <Col span={8}>
            {/* Order Summary */}
            <Card 
              title={
                <Space>
                  <DollarOutlined />
                  Thông tin bổ sung
                </Space>
              }
              style={{ marginBottom: 24 }}
            >
              <Form.Item name="ngay_dat_hang" label="Ngày đặt hàng">
                <DatePicker 
                  style={{ width: '100%' }} 
                  format="DD/MM/YYYY"
                />
              </Form.Item>

              <Form.Item name="trang_thai" label="Trạng thái">
                <Select>
                  <Option value="cho_xu_ly">Chờ xác nhận</Option>
                  <Option value="da_xac_nhan">Đã xác nhận</Option>
                  <Option value="dang_giao">Đang giao</Option>
                  <Option value="hoan_thanh">Hoàn thành</Option>
                  <Option value="huy">Hủy</Option>
                </Select>
              </Form.Item>

              <Form.Item name="ghi_chu" label="Ghi chú đơn hàng">
                <TextArea 
                  rows={3} 
                  placeholder="Ghi chú thêm về đơn hàng"
                />
              </Form.Item>
            </Card>

            {/* Payment Summary */}
            <Card title="Đóng gói và giao hàng">
              <div style={{ marginBottom: 16 }}>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Text>Tổng tiền ({selectedProducts.length} sản phẩm)</Text>
                  <Text strong>{subtotal.toLocaleString('vi-VN')}đ</Text>
                </Space>
              </div>

              <div style={{ marginBottom: 16 }}>
                <Row gutter={8}>
                  <Col span={16}>
                    <Form.Item name="giam_gia" style={{ margin: 0 }}>
                      <InputNumber
                        placeholder="Giảm giá"
                        style={{ width: '100%' }}
                        min={0}
                        max={discountType === 'percent' ? 100 : subtotal}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Select
                      value={discountType}
                      onChange={setDiscountType}
                      style={{ width: '100%' }}
                    >
                      <Option value="amount">VNĐ</Option>
                      <Option value="percent">%</Option>
                    </Select>
                  </Col>
                </Row>
              </div>

              <Divider style={{ margin: '16px 0' }} />

              <div style={{ marginBottom: 16 }}>
                <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                  <Text strong>Khách phải trả</Text>
                  <Text strong style={{ fontSize: 16, color: '#1890ff' }}>
                    {total.toLocaleString('vi-VN')}đ
                  </Text>
                </Space>
              </div>

              <div style={{ marginTop: 16 }}>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  Bạn hãy thêm thông tin khách hàng để sử dụng được ưu đãi tại đây
                </Text>
              </div>
            </Card>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

export default CreateOrderDemo;
