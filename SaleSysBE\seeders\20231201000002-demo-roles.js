'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert('vai_tro', [
      {
        ma_vai_tro: 'ADMIN',
        ten_vai_tro: 'Quản trị viên',
        mo_ta: '<PERSON><PERSON> tất cả quyền trong hệ thống'
      },
      {
        ma_vai_tro: 'QUAN_LY',
        ten_vai_tro: 'Quản lý',
        mo_ta: 'Quản lý các hoạt động kinh doanh'
      },
      {
        ma_vai_tro: 'NHAN_VIEN_BAN_HANG',
        ten_vai_tro: 'Nhân viên bán hàng',
        mo_ta: 'Thực hiện các giao dịch bán hàng'
      },
      {
        ma_vai_tro: 'NHAN_VIEN_KHO',
        ten_vai_tro: 'Nhân viên kho',
        mo_ta: '<PERSON>uản lý kho hàng và tồn kho'
      },
      {
        ma_vai_tro: 'KE_TOAN',
        ten_vai_tro: '<PERSON>ế toán',
        mo_ta: 'Quản lý tài chính và báo cáo'
      }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('vai_tro', null, {});
  }
};
