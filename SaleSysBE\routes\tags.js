const express = require('express');
const { asyncHandler } = require('../middleware/errorHandler');
const { requirePermission } = require('../middleware/auth');
const router = express.Router();

const {
  getTags,
  getTag,
  createTag,
  updateTag,
  deleteTag
} = require('../controllers/tagController');

// <PERSON><PERSON><PERSON> danh sách tags
router.get('/', asyncHandler(getTags));

// Lấy chi tiết tag
router.get('/:id', asyncHandler(getTag));

// Tạo tag mới
router.post('/', requirePermission('THEM_TAG'), asyncHandler(createTag));

// Cập nhật tag
router.put('/:id', requirePermission('SUA_TAG'), asyncHandler(updateTag));

// Xóa tag
router.delete('/:id', requirePermission('XOA_TAG'), asyncHandler(deleteTag));

module.exports = router;