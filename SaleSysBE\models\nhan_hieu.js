'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Nhan<PERSON>ieu extends Model {
    static associate(models) {
      // Quan hệ với sản phẩm (1-n)
      NhanHieu.hasMany(models.SanPham, {
        foreignKey: 'nhan_hieu_id',
        as: 'sanPhamList'
      });
    }
  }

  NhanHieu.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    ten: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    mo_ta: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'NhanHieu',
    tableName: 'nhan_hieu'
  });

  return <PERSON><PERSON><PERSON><PERSON>;
};
