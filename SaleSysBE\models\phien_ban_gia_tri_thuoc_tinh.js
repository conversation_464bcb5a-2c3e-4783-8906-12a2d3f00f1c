'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class PhienBanGiaTriThuocTinh extends Model {
    static associate(models) {
      // Quan hệ với phiên bản sản phẩm
      PhienBanGiaTriThuocTinh.belongsTo(models.PhienBanSanPham, {
        foreignKey: 'phien_ban_san_pham_id',
        as: 'phienBanSanPham'
      });

      // Quan hệ với giá trị thuộc tính
      PhienBanGiaTriThuocTinh.belongsTo(models.GiaTriThuocTinh, {
        foreignKey: 'gia_tri_thuoc_tinh_id',
        as: 'giaTriThuocTinh'
      });
    }
  }

  PhienBanGiaTriThuocTinh.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    phien_ban_san_pham_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'phien_ban_san_pham',
        key: 'id'
      }
    },
    gia_tri_thuoc_tinh_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'gia_tri_thuoc_tinh',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'PhienBanGiaTriThuocTinh',
    tableName: 'phien_ban_gia_tri_thuoc_tinh',
    timestamps: false,
    indexes: [
      {
        unique: true,
        fields: ['phien_ban_san_pham_id', 'gia_tri_thuoc_tinh_id'],
        name: 'pb_gt_thuoc_tinh_unique'
      }
    ]
  });

  return PhienBanGiaTriThuocTinh;
};
