import React from 'react';
import { Modal, Descriptions, Row, Col, Statistic, Card, Tag, Divider } from 'antd';
import { 
  ShopOutlined, 
  EnvironmentOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined
} from '@ant-design/icons';

const WarehouseDetail = ({ visible, onCancel, warehouse, loading }) => {
  if (!warehouse) return null;

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString('vi-VN');
  };

  const getStatusTag = (status) => {
    if (status === 'active') {
      return <Tag color="success" icon={<CheckCircleOutlined />}>Hoạt động</Tag>;
    }
    return <Tag color="error" icon={<CloseCircleOutlined />}>Ngừng hoạt động</Tag>;
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <ShopOutlined />
          Chi tiết kho hàng: {warehouse.ten_kho}
        </div>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={900}
      loading={loading}
    >
      {/* Statistics Cards */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card size="small" hoverable>
            <Statistic
              title="Tổng sản phẩm"
              value={warehouse.tong_san_pham || 0}
              prefix={<ShopOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff', fontSize: '24px' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small" hoverable>
            <Statistic
              title="Tổng tồn kho"
              value={warehouse.tong_ton_kho || 0}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a', fontSize: '24px' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card size="small" hoverable>
            <div style={{ textAlign: 'center' }}>
              <div style={{ color: '#666', fontSize: '14px', marginBottom: '8px' }}>
                Trạng thái
              </div>
              <div style={{ fontSize: '16px' }}>
                {getStatusTag(warehouse.trang_thai)}
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      <Divider orientation="left">Thông tin cơ bản</Divider>

      {/* Basic Information */}
      <Descriptions bordered column={2} size="middle">
        <Descriptions.Item 
          label={<><ShopOutlined /> Tên kho hàng</>}
          span={2}
        >
          <strong style={{ fontSize: '16px' }}>{warehouse.ten_kho}</strong>
        </Descriptions.Item>
        
        <Descriptions.Item 
          label={<><EnvironmentOutlined /> Địa chỉ</>}
          span={2}
        >
          {warehouse.dia_chi}
        </Descriptions.Item>
        
        <Descriptions.Item 
          label={<><FileTextOutlined /> Mô tả</>}
          span={2}
        >
          {warehouse.mo_ta || 'Không có mô tả'}
        </Descriptions.Item>
        
        <Descriptions.Item label={<><UserOutlined /> Người tạo</>}>
          {warehouse.nguoi_tao || 'N/A'}
        </Descriptions.Item>
        
        <Descriptions.Item label={<><CalendarOutlined /> Ngày tạo</>}>
          {formatDate(warehouse.ngay_tao)}
        </Descriptions.Item>
        
        <Descriptions.Item label={<><UserOutlined /> Người cập nhật</>}>
          {warehouse.nguoi_cap_nhap || 'N/A'}
        </Descriptions.Item>
        
        <Descriptions.Item label={<><CalendarOutlined /> Ngày cập nhật</>}>
          {formatDate(warehouse.ngay_cap_nhap)}
        </Descriptions.Item>
      </Descriptions>

      <Divider orientation="left">Thống kê chi tiết</Divider>

      {/* Detailed Statistics */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="Thông tin tồn kho" size="small">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Tổng số sản phẩm">
                <strong>{warehouse.tong_san_pham || 0}</strong> sản phẩm
              </Descriptions.Item>
              <Descriptions.Item label="Tổng số lượng tồn">
                <strong>{warehouse.tong_ton_kho || 0}</strong> đơn vị
              </Descriptions.Item>
              <Descriptions.Item label="Giá trị tồn kho">
                <strong style={{ color: '#52c41a' }}>
                  {((warehouse.gia_tri_ton_kho || 0)).toLocaleString('vi-VN')} VNĐ
                </strong>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="Trạng thái hoạt động" size="small">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Trạng thái hiện tại">
                {getStatusTag(warehouse.trang_thai)}
              </Descriptions.Item>
              <Descriptions.Item label="Thời gian hoạt động">
                {warehouse.ngay_tao ? 
                  `${Math.ceil((new Date() - new Date(warehouse.ngay_tao)) / (1000 * 60 * 60 * 24))} ngày` : 
                  'N/A'
                }
              </Descriptions.Item>
              <Descriptions.Item label="Lần cập nhật cuối">
                {warehouse.ngay_cap_nhap ? 
                  formatDate(warehouse.ngay_cap_nhap) : 
                  'Chưa cập nhật'
                }
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </Modal>
  );
};

export default WarehouseDetail;
