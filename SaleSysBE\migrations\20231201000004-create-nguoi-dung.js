'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('nguoi_dung', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      ho_ten: {
        type: Sequelize.STRING,
        allowNull: false
      },
      so_dien_thoai: {
        type: Sequelize.STRING,
        allowNull: true
      },
      email: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true
      },
      mat_khau: {
        type: Sequelize.STRING,
        allowNull: true
      },
      loai_nguoi_dung: {
        type: Sequelize.ENUM('khach_hang', 'nhan_vien', 'admin'),
        defaultValue: 'khach_hang'
      },
      trang_thai: {
        type: Sequelize.ENUM('dang_giao_dich', 'ngung_giao_dich', 'tam_khoa'),
        defaultValue: 'dang_giao_dich'
      },
      nguoi_tao: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nguoi_cap_nhap: {
        type: Sequelize.STRING,
        allowNull: true
      },
      ngay_tao: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      ngay_cap_nhap: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('nguoi_dung');
  }
};
