const express = require('express');
const router = express.Router();

const {
  uploadSingleImage,
  uploadMultipleImagesController,
  deleteImageController,
  uploadProductImage
} = require('../controllers/uploadController');

const {
  uploadSingle,
  uploadMultiple,
  handleUploadError
} = require('../middleware/uploadMiddleware');

// Test route
router.get('/test', (req, res) => {
  res.json({
    success: true,
    message: 'Upload routes working!',
    timestamp: new Date().toISOString()
  });
});

// Route upload single image
router.post('/single', uploadSingle('image'), uploadSingleImage);

// Route test simple upload
router.post('/simple', uploadSingle('image'), uploadSingleImage);

// Route upload multiple images
router.post('/multiple', uploadMultiple('images', 10), uploadMultipleImagesController);

// Route upload ảnh sản phẩm
router.post('/product', uploadSingle('image'), uploadProductImage);

// Route xóa ảnh
router.delete('/:publicId', deleteImageController);

// Error handler middleware
router.use(handleUploadError);

module.exports = router;
