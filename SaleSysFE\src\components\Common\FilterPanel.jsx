import { useState } from 'react';
import {
  Drawer,
  Button,
  Space,
  Row,
  Col,
  Select,
  Input,
  DatePicker,
  InputNumber,
  Checkbox,
  Radio,
  Typography,
  Divider,
  Form
} from 'antd';
import {
  FilterOutlined,
  CloseOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

const FilterPanel = ({
  // Panel props
  title = "Bộ lọc",
  placement = "right",
  width = 400,
  
  // Filter configuration
  filters = [],
  
  // Event handlers
  onFilter,
  onReset,
  onClose,
  
  // Initial values
  initialValues = {},
  
  // Visibility
  visible = false,
  
  // Style props
  className,
  style
}) => {
  const [form] = Form.useForm();
  const [filterValues, setFilterValues] = useState(initialValues);

  // Handle filter value change
  const handleValueChange = (changedValues, allValues) => {
    setFilterValues(allValues);
  };

  // Handle apply filter
  const handleApply = () => {
    const values = form.getFieldsValue();
    if (onFilter) {
      onFilter(values);
    }
    if (onClose) {
      onClose();
    }
  };

  // Handle reset
  const handleReset = () => {
    form.resetFields();
    setFilterValues({});
    if (onReset) {
      onReset();
    }
  };

  // Render filter input based on type
  const renderFilterInput = (filter) => {
    const commonProps = {
      placeholder: filter.placeholder,
      style: { width: '100%' },
      allowClear: true
    };

    switch (filter.type) {
      case 'select':
        return (
          <Select {...commonProps} mode={filter.multiple ? 'multiple' : undefined}>
            {filter.options?.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
      
      case 'multiSelect':
        return (
          <Select {...commonProps} mode="multiple">
            {filter.options?.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        );
      
      case 'dateRange':
        return (
          <RangePicker
            {...commonProps}
            placeholder={filter.placeholder || ['Từ ngày', 'Đến ngày']}
          />
        );
      
      case 'date':
        return (
          <DatePicker {...commonProps} />
        );
      
      case 'number':
        return (
          <InputNumber
            {...commonProps}
            min={filter.min}
            max={filter.max}
            step={filter.step}
          />
        );
      
      case 'numberRange':
        return (
          <Input.Group compact>
            <Form.Item
              name={[filter.key, 'min']}
              style={{ width: '50%', marginBottom: 0 }}
            >
              <InputNumber
                placeholder="Từ"
                style={{ width: '100%' }}
                min={filter.min}
                max={filter.max}
              />
            </Form.Item>
            <Form.Item
              name={[filter.key, 'max']}
              style={{ width: '50%', marginBottom: 0 }}
            >
              <InputNumber
                placeholder="Đến"
                style={{ width: '100%' }}
                min={filter.min}
                max={filter.max}
              />
            </Form.Item>
          </Input.Group>
        );
      
      case 'checkbox':
        return (
          <Checkbox.Group>
            <Row>
              {filter.options?.map((option) => (
                <Col span={24} key={option.value} style={{ marginBottom: 8 }}>
                  <Checkbox value={option.value}>
                    {option.label}
                  </Checkbox>
                </Col>
              ))}
            </Row>
          </Checkbox.Group>
        );
      
      case 'radio':
        return (
          <Radio.Group>
            <Row>
              {filter.options?.map((option) => (
                <Col span={24} key={option.value} style={{ marginBottom: 8 }}>
                  <Radio value={option.value}>
                    {option.label}
                  </Radio>
                </Col>
              ))}
            </Row>
          </Radio.Group>
        );
      
      case 'text':
      default:
        return (
          <Input {...commonProps} />
        );
    }
  };

  // Group filters by category
  const groupedFilters = filters.reduce((groups, filter) => {
    const category = filter.category || 'default';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(filter);
    return groups;
  }, {});

  return (
    <Drawer
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <FilterOutlined />
          <span>{title}</span>
        </div>
      }
      placement={placement}
      width={width}
      open={visible}
      onClose={onClose}
      className={className}
      style={style}
      footer={
        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={handleReset} icon={<ReloadOutlined />}>
              Đặt lại
            </Button>
            <Button type="primary" onClick={handleApply}>
              Áp dụng
            </Button>
          </Space>
        </div>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onValuesChange={handleValueChange}
      >
        {Object.entries(groupedFilters).map(([category, categoryFilters]) => (
          <div key={category}>
            {category !== 'default' && (
              <>
                <Title level={5} style={{ marginTop: 16, marginBottom: 12 }}>
                  {category}
                </Title>
                <Divider style={{ margin: '8px 0 16px 0' }} />
              </>
            )}
            
            {categoryFilters.map((filter) => (
              <Form.Item
                key={filter.key}
                name={filter.key}
                label={filter.label}
                style={{ marginBottom: 16 }}
              >
                {renderFilterInput(filter)}
              </Form.Item>
            ))}
          </div>
        ))}
      </Form>
    </Drawer>
  );
};

export default FilterPanel;
