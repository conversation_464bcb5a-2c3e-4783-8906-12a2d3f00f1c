'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class PhanCongNhanVienPhuTrach extends Model {
    static associate(models) {
      // Quan hệ với người dùng (kh<PERSON><PERSON> hàng)
      PhanCongNhanVienPhuTrach.belongsTo(models.NguoiDung, {
        foreignKey: 'nguoi_dung_id',
        as: 'nguoiDung'
      });

      // Quan hệ với nhân viên
      Phan<PERSON>ongNhanVienPhuTrach.belongsTo(models.NguoiDung, {
        foreignKey: 'nhan_vien_id',
        as: 'nhanVien'
      });
    }
  }

  PhanCongNhanVienPhuTrach.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nguoi_dung_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    nhan_vien_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    ngay_phan_cong: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'PhanCongNhanVienPhuTrach',
    tableName: 'phan_cong_nhan_vien_phu_trach',
    timestamps: false
  });

  return PhanCongNhanVienPhuTrach;
};
