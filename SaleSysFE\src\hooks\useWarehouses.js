import { useQuery, useMutation, useQueryClient } from 'react-query';
import { message } from 'antd';
import { warehousesAPI } from '../services/api';

// Query keys
export const WAREHOUSE_QUERY_KEYS = {
  all: ['warehouses'],
  lists: () => [...WAREHOUSE_QUERY_KEYS.all, 'list'],
  list: (filters) => [...WAREHOUSE_QUERY_KEYS.lists(), filters],
  details: () => [...WAREHOUSE_QUERY_KEYS.all, 'detail'],
  detail: (id) => [...WAREHOUSE_QUERY_KEYS.details(), id],
  inventory: () => [...WAREHOUSE_QUERY_KEYS.all, 'inventory'],
  movements: () => [...WAREHOUSE_QUERY_KEYS.all, 'movements'],
  stockChecks: () => [...WAREHOUSE_QUERY_KEYS.all, 'stock-checks'],
};

// Get warehouses list
export const useWarehouses = (params = {}) => {
  return useQuery(
    WAREHOUSE_QUERY_KEYS.list(params),
    () => warehousesAPI.getWarehouses(params),
    {
      select: (data) => {
        console.log('🔍 API Response Debug:');
        console.log('- Full response:', data);
        console.log('- data keys:', Object.keys(data || {}));
        console.log('- data.data:', data?.data);
        console.log('- data.data type:', typeof data?.data);
        console.log('- data.data is array:', Array.isArray(data?.data));
        console.log('- data.success:', data?.success);

        // Try different possible structures
        let result = data?.data.data||[];
        if (Array.isArray(data?.data?.data)) {
          result = data.data.data;
        } else if (Array.isArray(data)) {
          result = data;
        } else if (data?.warehouses && Array.isArray(data.warehouses)) {
          result = data.warehouses;
        } else if (data?.items && Array.isArray(data.items)) {
          result = data.items;
        }

        console.log('- Final result:', result);
        console.log('- Final result length:', result.length);
        return result;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
    }
  );
};

// Get single warehouse
export const useWarehouse = (id) => {
  return useQuery(
    WAREHOUSE_QUERY_KEYS.detail(id),
    () => warehousesAPI.getWarehouse(id),
    {
      enabled: !!id,
      select: (data) => data.data,
    }
  );
};

// Create warehouse mutation
export const useCreateWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation(warehousesAPI.createWarehouse, {
    onSuccess: (data) => {
      message.success('Tạo kho hàng thành công!');
      queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.lists());
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi tạo kho hàng');
    },
  });
};

// Update warehouse mutation
export const useUpdateWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, data }) => warehousesAPI.updateWarehouse(id, data),
    {
      onSuccess: (data, variables) => {
        message.success('Cập nhật kho hàng thành công!');
        queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.lists());
        queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.detail(variables.id));
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật kho hàng');
      },
    }
  );
};

// Delete warehouse mutation
export const useDeleteWarehouse = () => {
  const queryClient = useQueryClient();

  return useMutation(warehousesAPI.deleteWarehouse, {
    onSuccess: () => {
      message.success('Xóa kho hàng thành công!');
      queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.lists());
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa kho hàng');
    },
  });
};

// Get inventory
export const useInventory = (params = {}) => {
  return useQuery(
    [...WAREHOUSE_QUERY_KEYS.inventory(), params],
    () => warehousesAPI.getInventory(params),
    {
      select: (response) => ({
        data: Array.isArray(response?.data?.data) ? response.data.data : [],
        statistics: response?.data?.statistics || {},
        pagination: response?.data?.pagination || {}
      }),
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
};

// Adjust inventory mutation
export const useAdjustInventory = () => {
  const queryClient = useQueryClient();

  return useMutation(warehousesAPI.adjustInventory, {
    onSuccess: () => {
      message.success('Điều chỉnh tồn kho thành công!');
      queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.inventory());
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi điều chỉnh tồn kho');
    },
  });
};

// Get stock movements
export const useStockMovements = (params = {}) => {
  return useQuery(
    [...WAREHOUSE_QUERY_KEYS.movements(), params],
    () => warehousesAPI.getStockMovements(params),
    {
      select: (data) => data.data,
      staleTime: 1 * 60 * 1000, // 1 minute
    }
  );
};

// Create stock movement mutation
export const useCreateStockMovement = () => {
  const queryClient = useQueryClient();

  return useMutation(warehousesAPI.createStockMovement, {
    onSuccess: () => {
      message.success('Tạo giao dịch thành công!');
      queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.movements());
      queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.inventory());
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi tạo giao dịch');
    },
  });
};

// Get stock checks
export const useStockChecks = (params = {}) => {
  return useQuery(
    [...WAREHOUSE_QUERY_KEYS.stockChecks(), params],
    () => warehousesAPI.getStockChecks(params),
    {
      select: (data) => {
        console.log('🔍 useStockChecks raw response:', data);
        console.log('🔍 useStockChecks data.data:', data.data);
        console.log('🔍 useStockChecks pagination:', data.pagination);

        // Return both data and pagination
        return {
          data: Array.isArray(data?.data.data) ? data.data.data : [],
          pagination: data?.data.pagination || { total: 0, page: 1, limit: 10, totalPages: 0 },
          success: data?.data.success || false
        };
      },
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
};

// Get single stock check
export const useStockCheck = (id) => {
  return useQuery(
    [...WAREHOUSE_QUERY_KEYS.stockChecks(), id],
    () => warehousesAPI.getStockCheck(id),
    {
      enabled: !!id,
      select: (data) => data.data,
    }
  );
};

// Create stock check mutation
export const useCreateStockCheck = () => {
  const queryClient = useQueryClient();

  return useMutation(warehousesAPI.createStockCheck, {
    onSuccess: () => {
      message.success('Tạo phiếu kiểm kê thành công!');
      queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.stockChecks());
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi tạo phiếu kiểm kê');
    },
  });
};

// Update stock check mutation
export const useUpdateStockCheck = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, data }) => warehousesAPI.updateStockCheck(id, data),
    {
      onSuccess: (data, variables) => {
        message.success('Cập nhật phiếu kiểm kê thành công!');
        queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.stockChecks());
        queryClient.invalidateQueries([...WAREHOUSE_QUERY_KEYS.stockChecks(), variables.id]);
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật phiếu kiểm kê');
      },
    }
  );
};

// Delete stock check mutation
export const useDeleteStockCheck = () => {
  const queryClient = useQueryClient();

  return useMutation(warehousesAPI.deleteStockCheck, {
    onSuccess: () => {
      message.success('Xóa phiếu kiểm kê thành công!');
      queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.stockChecks());
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa phiếu kiểm kê');
    },
  });
};

// Get stock check details
export const useStockCheckDetails = (id, params = {}) => {
  return useQuery(
    ['stockCheckDetails', id, params],
    () => warehousesAPI.getStockCheckDetails(id, params),
    {
      enabled: !!id,
      select: (data) => data.data.data,
      staleTime: 30 * 1000, // 30 seconds
    }
  );
};

// Update stock check item mutation
export const useUpdateStockCheckItem = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ stockCheckId, itemId, ...data }) => warehousesAPI.updateStockCheckItem(stockCheckId, itemId, data),
    {
      onSuccess: (data, variables) => {
        message.success('Cập nhật số lượng thành công!');
        queryClient.invalidateQueries(['stockCheckDetails', variables.stockCheckId]);
        queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.stockChecks());
        // Cập nhật inventory cache để hiển thị số liệu mới nhất
        queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.inventory());
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi cập nhật số lượng');
      },
    }
  );
};

// Complete stock check mutation
export const useCompleteStockCheck = () => {
  const queryClient = useQueryClient();

  return useMutation(warehousesAPI.completeStockCheck, {
    onSuccess: (data, id) => {
      message.success('Cân bằng kho thành công! Tồn kho đã được cập nhật.');
      queryClient.invalidateQueries(['stockCheckDetails', id]);
      queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.stockChecks());
      // ✅ QUAN TRỌNG: Invalidate inventory cache để cập nhật tồn kho
      queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.inventory());
      // Invalidate stock movements để cập nhật lịch sử
      queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.movements());
    },
    onError: (error) => {
      message.error(error.response?.data?.message || 'Có lỗi xảy ra khi cân bằng kho');
    },
  });
};

// Get available products for stock check
export const useAvailableProducts = (id, params = {}) => {
  return useQuery(
    ['availableProducts', id, params],
    () => warehousesAPI.getAvailableProducts(id, params),
    {
      enabled: !!id,
      select: (data) => data.data,
      staleTime: 30 * 1000, // 30 seconds
    }
  );
};

// Add product to stock check mutation
export const useAddProductToStockCheck = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ id, ...data }) => warehousesAPI.addProductToStockCheck(id, data),
    {
      onSuccess: (data, variables) => {
        message.success('Thêm sản phẩm vào phiếu kiểm kê thành công!');
        queryClient.invalidateQueries(['stockCheckDetails', variables.id]);
        queryClient.invalidateQueries(['availableProducts', variables.id]);
        queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.stockChecks());
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi thêm sản phẩm');
      },
    }
  );
};

// Remove product from stock check mutation
export const useRemoveProductFromStockCheck = () => {
  const queryClient = useQueryClient();

  return useMutation(
    ({ stockCheckId, itemId }) => warehousesAPI.removeProductFromStockCheck(stockCheckId, itemId),
    {
      onSuccess: (data, variables) => {
        message.success('Xóa sản phẩm khỏi phiếu kiểm kê thành công!');
        queryClient.invalidateQueries(['stockCheckDetails', variables.stockCheckId]);
        queryClient.invalidateQueries(['availableProducts', variables.stockCheckId]);
        queryClient.invalidateQueries(WAREHOUSE_QUERY_KEYS.stockChecks());
      },
      onError: (error) => {
        message.error(error.response?.data?.message || 'Có lỗi xảy ra khi xóa sản phẩm');
      },
    }
  );
};

// Get product variants for stock movements (similar to inventory)
export const useProductVariantsForMovements = (params = {}) => {
  return useQuery(
    ['product-variants-movements', params],
    () => warehousesAPI.getInventory(params),
    {
      select: (data) => {
        // Transform inventory data to product variants format
        const variants = data.data?.data || [];
        console.log("variants", variants);
        return variants.map(item => ({
          id: item.phien_ban_id,
          ten: `${item.phien_ban}`,
          ma: item.ma_phien_ban,
          san_pham_id: item.san_pham_id,
          phien_ban_id: item.phien_ban_id,
          so_luong_ton: item.ton_kho,
          gia_ban_le: item.gia_ban,
          gia_ban_buon: item.gia_buon,
          gia_nhap: item.gia_nhap,
          ton_kho: item.ton_kho
        }));
      },
      staleTime: 2 * 60 * 1000, // 2 minutes
    }
  );
};
