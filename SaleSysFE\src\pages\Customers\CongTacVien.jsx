import React, { useState, useEffect } from 'react';
import { 
  Card, Typography, Button, Input, Tabs, Space, 
  message, Row, Col, Drawer, Form, Select,
  Checkbox, DatePicker, Divider, Tag
} from 'antd';
import { 
  PlusOutlined, SearchOutlined, DownloadOutlined, 
  UploadOutlined, FilterOutlined, ReloadOutlined
} from '@ant-design/icons';
import { PageHeader, DataTable } from '../../components/Common';
import { customerAPI  } from '../../services/api';
import { employeeAPI, tagAPI } from '../../services/api/index';
import { useQuery } from 'react-query';
import { useNavigate } from 'react-router-dom';
import CustomerForm from './CustomerForm';

const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;

const CongTacVien = () => {
  const [searchText, setSearchText] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [activeTab, setActiveTab] = useState('all');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [filterDrawerVisible, setFilterDrawerVisible] = useState(false);
  const [activeFilters, setActiveFilters] = useState({});
  const [filterForm] = Form.useForm();
  const navigate = useNavigate();

  // Fetch customers with type=ctv filter
  const { data: customersData, isLoading, refetch } = useQuery(
    ['customers-ctv', currentPage, pageSize, searchText, activeTab, activeFilters],
    () => customerAPI.getCustomers({
      page: currentPage,
      limit: pageSize,
      search: searchText,
      trang_thai: activeTab !== 'all' ? activeTab : undefined,
      loai: 'ctv', // Thêm filter loại = ctv
      ...activeFilters
    }),
    {
      keepPreviousData: true
    }
  );

  // Fetch customer groups for filter
  const { data: groupsData } = useQuery(
    'customerGroups',
    () => customerAPI.getAllCustomerGroup()
  );

  // Fetch employees for filter
  const { data: employeesData } = useQuery(
    'employees',
    () => employeeAPI.getEmployees({ limit: 100 })
  );

  // Fetch tags for filter
  const { data: tagsData } = useQuery(
    'tags',
    () => tagAPI.getTags()
  );

  const handleSearch = (value) => {
    setSearchText(value);
    setCurrentPage(1);
  };

  const handleTabChange = (key) => {
    setActiveTab(key);
    setCurrentPage(1);
  };

  const handlePageChange = (page, size) => {
    setCurrentPage(page);
    setPageSize(size);
  };

  const handleExport = async () => {
    try {
      const response = await customerAPI.exportCustomers({ loai: 'ctv' });
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'cong-tac-vien.xlsx');
      document.body.appendChild(link);
      link.click();
      link.remove();
      message.success('Xuất file thành công');
    } catch (error) {
      message.error('Xuất file thất bại');
    }
  };

  const handleImport = () => {
    // TODO: Implement import functionality
    message.info('Chức năng nhập file đang được phát triển');
  };

  const handleAddCustomer = () => {
    setIsModalVisible(true);
  };

  const handleViewCustomer = (record) => {
    navigate(`/customers/${record.id}`);
  };

  const showFilterDrawer = () => {
    setFilterDrawerVisible(true);
  };

  const closeFilterDrawer = () => {
    setFilterDrawerVisible(false);
  };

  const applyFilters = (values) => {
    // Xử lý các giá trị ngày tháng để chuyển thành chuỗi JSON
    const processedValues = { ...values };
    
    // Xử lý ngày sinh
    if (values.ngay_sinh) {
      processedValues.ngay_sinh = JSON.stringify([
        values.ngay_sinh[0].format('YYYY-MM-DD'),
        values.ngay_sinh[1].format('YYYY-MM-DD')
      ]);
    }
    
    // Xử lý khoảng ngày sinh
    if (values.khoang_ngay_sinh) {
      processedValues.khoang_ngay_sinh = JSON.stringify([
        values.khoang_ngay_sinh[0].format('YYYY-MM-DD'),
        values.khoang_ngay_sinh[1].format('YYYY-MM-DD')
      ]);
    }
    
    // Xử lý ngày tạo
    if (values.ngay_tao) {
      processedValues.ngay_tao = JSON.stringify([
        values.ngay_tao[0].format('YYYY-MM-DD'),
        values.ngay_tao[1].format('YYYY-MM-DD')
      ]);
    }
    
    // Xử lý ngày sinh cụ thể
    if (values.ngay_sinh_cu_the) {
      processedValues.ngay_sinh_cu_the = values.ngay_sinh_cu_the.format('YYYY-MM-DD');
    }
    
    // Xử lý danh sách trạng thái
    if (values.trang_thai_list && values.trang_thai_list.length > 0) {
      processedValues.trang_thai_list = JSON.stringify(values.trang_thai_list);
    }
    
    // Xử lý danh sách tags
    if (values.tags && values.tags.length > 0) {
      processedValues.tags = JSON.stringify(values.tags);
    }
    
    setActiveFilters(processedValues);
    setCurrentPage(1);
    closeFilterDrawer();
  };

  const resetFilters = () => {
    filterForm.resetFields();
    setActiveFilters({});
    setCurrentPage(1);
  };

  const columns = [
    {
      title: 'Mã CTV',
      dataIndex: 'ma_khach_hang',
      key: 'ma_khach_hang',
      width: 150,
    },
    {
      title: 'Tên cộng tác viên',
      dataIndex: 'ho_ten',
      key: 'ho_ten',
      sorter: true,
      render: (text, record) => (
        <a onClick={() => handleViewCustomer(record)}>{text}</a>
      ),
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'so_dien_thoai',
      key: 'so_dien_thoai',
    },
    {
      title: 'Nhóm CTV',
      dataIndex: 'nhomKhachHang',
      key: 'nhomKhachHang',
      render: (nhomKhachHang) => nhomKhachHang?.ten_nhom || 'Chưa phân nhóm'
    },
    {
      title: 'Trạng thái',
      dataIndex: 'trang_thai',
      key: 'trang_thai',
      width: 150,
      render: (status) => {
        const statusMap = {
          'dang_giao_dich': { color: 'green', text: 'Đang giao dịch' },
          'ngung_giao_dich': { color: 'red', text: 'Ngừng giao dịch' },
          'tam_khoa': { color: 'orange', text: 'Tạm khóa' }
        };
        
        const statusInfo = statusMap[status] || { color: 'default', text: 'Không xác định' };
        
        return (
          <Tag color={statusInfo.color}>
            {statusInfo.text}
          </Tag>
        );
      }
    },
    {
      title: 'Công nợ hiện tại',
      dataIndex: 'cong_no_hien_tai',
      key: 'cong_no_hien_tai',
      align: 'right',
      render: (value) => value?.toLocaleString() || '0',
      sorter: true,
    },
    {
      title: 'Tổng chi tiêu',
      dataIndex: 'tong_chi_tieu',
      key: 'tong_chi_tieu',
      align: 'right',
      render: (value) => value?.toLocaleString() || '0',
      sorter: true,
    },
    {
      title: 'Tổng SL đơn hàng',
      dataIndex: 'tong_sl_don_hang',
      key: 'tong_sl_don_hang',
      align: 'right',
      render: (value) => value?.toLocaleString() || '0',
    }
  ];

  return (
    <div>
      <PageHeader
        title="Cộng tác viên"
        actions={[
          {
            type: 'primary',
            icon: <PlusOutlined />,
            label: 'Thêm cộng tác viên',
            onClick: handleAddCustomer,
          },
        ]}
      />
      
      <Card style={{ marginBottom: 16 }}>
        <Tabs 
          activeKey={activeTab} 
          onChange={handleTabChange}
          tabBarExtraContent={
            <Space>
              <Button icon={<DownloadOutlined />} onClick={handleExport}>Xuất file</Button>
              <Button icon={<UploadOutlined />} onClick={handleImport}>Nhập file</Button>
              <Button 
                icon={<FilterOutlined />} 
                onClick={showFilterDrawer}
                type={Object.keys(activeFilters).length > 0 ? 'primary' : 'default'}
              >
                Bộ lọc
                {Object.keys(activeFilters).length > 0 && (
                  <Tag color="blue" style={{ marginLeft: 5 }}>
                    {Object.keys(activeFilters).length}
                  </Tag>
                )}
              </Button>
              <Button icon={<ReloadOutlined />} onClick={() => refetch()}>Làm mới</Button>
            </Space>
          }
        >
          <TabPane tab="Tất cả cộng tác viên" key="all" />
          <TabPane tab="Đang giao dịch" key="dang_giao_dich" />
          <TabPane tab="Ngừng giao dịch" key="ngung_giao_dich" />
          <TabPane tab="Tạm khóa" key="tam_khoa" />
        </Tabs>
        
        <Row style={{ marginTop: 16, marginBottom: 16 }}>
          <Col span={16}>
            <Input
              placeholder="Tìm kiếm theo mã CTV, tên, SĐT cộng tác viên"
              prefix={<SearchOutlined />}
              onChange={(e) => handleSearch(e.target.value)}
              style={{ width: '100%' }}
              allowClear
            />
          </Col>
        </Row>
        
        <DataTable
          columns={columns}
          dataSource={customersData?.data?.data?.customers || []}
          loading={isLoading}
          rowKey="id"
          showSearch={false}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: customersData?.data?.data?.pagination?.total || 0,
            onChange: handlePageChange,
            showSizeChanger: true,
            showTotal: (total) => `Tổng ${total} cộng tác viên`
          }}
          rowSelection={{
            type: 'checkbox',
          }}
        />
      </Card>

      {/* Customer Form Modal */}
      {isModalVisible && (
        <CustomerForm
          visible={isModalVisible}
          onCancel={() => setIsModalVisible(false)}
          onSuccess={() => {
            setIsModalVisible(false);
            refetch();
            message.success('Thêm cộng tác viên thành công');
          }}
          initialData={{ loai: 'ctv' }} // Thêm loại mặc định là ctv
        />
      )}

      {/* Filter Drawer */}
      <Drawer
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>Bộ lọc</span>
            <Button type="link" onClick={resetFilters} style={{ padding: 0 }}>
              Xóa bộ lọc
            </Button>
          </div>
        }
        placement="right"
        onClose={closeFilterDrawer}
        open={filterDrawerVisible}
        width={350}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={closeFilterDrawer}>Hủy</Button>
              <Button type="primary" onClick={() => filterForm.submit()}>
                Lọc
              </Button>
            </Space>
          </div>
        }
      >
        <Form
          form={filterForm}
          layout="vertical"
          onFinish={applyFilters}
          initialValues={activeFilters}
        >
          <div style={{ marginBottom: 16 }}>
            <Typography.Text type="secondary">Nổi bật</Typography.Text>
          </div>

          {/* Nhóm khách hàng */}
          <div style={{ marginBottom: 16 }}>
            <Typography.Text strong>Nhóm cộng tác viên</Typography.Text>
            <div style={{ marginTop: 8 }}>
              <Form.Item name="nhom_khach_hang_id" noStyle>
                <Select
                  placeholder="Tìm kiếm"
                  style={{ width: '100%' }}
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  dropdownRender={menu => (
                    <>
                      <Input.Search placeholder="Tìm kiếm" style={{ padding: '8px' }} />
                      <Divider style={{ margin: '4px 0' }} />
                      {menu}
                    </>
                  )}
                >
                  {groupsData?.data?.data?.map(group => (
                    <Option key={group.id} value={group.id}>
                      <Checkbox checked={activeFilters.nhom_khach_hang_id === group.id} />
                      <span style={{ marginLeft: 8 }}>{group.ten_nhom}</span>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </div>

          {/* Giới tính */}
          <div style={{ marginBottom: 16 }}>
            <Typography.Text strong>Giới tính</Typography.Text>
            <div style={{ marginTop: 8 }}>
              <Form.Item name="gioi_tinh" noStyle>
                <Select
                  placeholder="Chọn giới tính"
                  style={{ width: '100%' }}
                  allowClear
                >
                  <Option value="nam">Nam</Option>
                  <Option value="nu">Nữ</Option>
                  <Option value="khac">Khác</Option>
                </Select>
              </Form.Item>
            </div>
          </div>

          {/* Ngày/Tháng sinh */}
          <div style={{ marginBottom: 16 }}>
            <Typography.Text strong>Ngày/Tháng sinh</Typography.Text>
            <div style={{ marginTop: 8 }}>
              <Form.Item name="ngay_sinh" noStyle>
                <RangePicker style={{ width: '100%' }} format="DD/MM/YYYY" />
              </Form.Item>
            </div>
          </div>

          {/* Nhân viên phụ trách */}
          <div style={{ marginBottom: 16 }}>
            <Typography.Text strong>Nhân viên phụ trách</Typography.Text>
            <div style={{ marginTop: 8 }}>
              <Form.Item name="nhan_vien_id" noStyle>
                <Select
                  placeholder="Chọn nhân viên"
                  style={{ width: '100%' }}
                  allowClear
                  showSearch
                  optionFilterProp="children"
                >
                  {employeesData?.data?.data?.employees?.map(employee => (
                    <Option key={employee.id} value={employee.id}>
                      {employee.ho_ten}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </div>

          {/* Trạng thái */}
          <div style={{ marginBottom: 16 }}>
            <Typography.Text strong>Trạng thái</Typography.Text>
            <div style={{ marginTop: 8 }}>
              <Form.Item name="trang_thai_list" noStyle>
                <Checkbox.Group style={{ width: '100%', display: 'flex', flexDirection: 'column', gap: '8px' }}>
                  <Checkbox value="dang_giao_dich">Đang giao dịch</Checkbox>
                  <Checkbox value="ngung_giao_dich">Ngừng giao dịch</Checkbox>
                  <Checkbox value="tam_khoa">Tạm khóa</Checkbox>
                </Checkbox.Group>
              </Form.Item>
            </div>
          </div>

          {/* Tags */}
          <div style={{ marginBottom: 16 }}>
            <Typography.Text strong>Tags</Typography.Text>
            <div style={{ marginTop: 8 }}>
              <Form.Item name="tags" noStyle>
                <Select
                  placeholder="Chọn tags"
                  style={{ width: '100%' }}
                  allowClear
                  mode="multiple"
                  showSearch
                  optionFilterProp="children"
                >
                  {tagsData?.data?.data?.map(tag => (
                    <Option key={tag.id} value={tag.id}>
                      {tag.ten_tag}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </div>

          {/* Ngày tạo */}
          <div style={{ marginBottom: 16 }}>
            <Typography.Text strong>Ngày tạo</Typography.Text>
            <div style={{ marginTop: 8 }}>
              <Form.Item name="ngay_tao" noStyle>
                <RangePicker style={{ width: '100%' }} format="DD/MM/YYYY" />
              </Form.Item>
            </div>
          </div>

          {/* Khoảng ngày sinh */}
          <div style={{ marginBottom: 16 }}>
            <Typography.Text strong>Khoảng ngày sinh</Typography.Text>
            <div style={{ marginTop: 8 }}>
              <Form.Item name="khoang_ngay_sinh" noStyle>
                <RangePicker style={{ width: '100%' }} format="DD/MM/YYYY" />
              </Form.Item>
            </div>
          </div>

          {/* Ngày sinh */}
          <div style={{ marginBottom: 16 }}>
            <Typography.Text strong>Ngày sinh</Typography.Text>
            <div style={{ marginTop: 8 }}>
              <Form.Item name="ngay_sinh_cu_the" noStyle>
                <DatePicker style={{ width: '100%' }} format="DD/MM/YYYY" />
              </Form.Item>
            </div>
          </div>
        </Form>
      </Drawer>
    </div>
  );
};

export default CongTacVien;