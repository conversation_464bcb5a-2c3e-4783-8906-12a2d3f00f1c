import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  Card, 
  Descriptions, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Typography, 
  Divider,
  Row,
  Col,
  Spin,
  Alert
} from 'antd';
import { 
  ArrowLeftOutlined, 
  EditOutlined, 
  PrinterOutlined,
  DownloadOutlined 
} from '@ant-design/icons';
import { useQuery } from 'react-query';
import { testAPI } from '../../services/api';
import { ORDER_STATUS_LABELS, ORDER_STATUS_COLORS } from '../../constants/orderStatus';

const { Title, Text } = Typography;

// Hook để lấy chi tiết đơn hàng
const useOrderDetail = (id) => {
  return useQuery(
    ['order', id],

    () => testAPI.getOrder(id),
    {
      enabled: !!id,
      retry: 1,
      refetchOnWindowFocus: false
    }
  );
};

const OrderDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { data, isLoading, error } = useOrderDetail(id);

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <Alert
        message="Lỗi"
        description={error.message}
        type="error"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  const order = data?.data;
  if (!order) {
    return (
      <Alert
        message="Không tìm thấy đơn hàng"
        type="warning"
        showIcon
        style={{ margin: '20px' }}
      />
    );
  }

  // Cấu hình cột cho bảng sản phẩm
  const productColumns = [
    {
      title: 'STT',
      key: 'index',
      width: 60,
      render: (_, __, index) => index + 1
    },
    {
      title: 'Tên sản phẩm',
      dataIndex: 'ten_san_pham',
      key: 'ten_san_pham'
    },
    {
      title: 'Số lượng',
      dataIndex: 'so_luong',
      key: 'so_luong',
      width: 100,
      align: 'center'
    },
    {
      title: 'Đơn giá',
      dataIndex: 'don_gia',
      key: 'don_gia',
      width: 120,
      align: 'right',
      render: (value) => new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(value)
    },
    {
      title: 'Thành tiền',
      dataIndex: 'thanh_tien',
      key: 'thanh_tien',
      width: 120,
      align: 'right',
      render: (value) => new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(value)
    }
  ];

  // Lấy thông tin trạng thái
  const statusInfo = {
    label: ORDER_STATUS_LABELS[order.trang_thai] || 'Không xác định',
    color: ORDER_STATUS_COLORS[order.trang_thai] || 'default'
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/orders')}
          >
            Quay lại
          </Button>
          <Title level={3} style={{ margin: 0 }}>
            Chi tiết đơn hàng {order.ma_don_hang}
          </Title>
        </Space>
        
        <div style={{ float: 'right' }}>
          <Space>
            <Button icon={<PrinterOutlined />}>
              In đơn hàng
            </Button>
            <Button icon={<DownloadOutlined />}>
              Xuất PDF
            </Button>
            <Button 
              type="primary" 
              icon={<EditOutlined />}
              onClick={() => navigate(`/orders/${id}/edit`)}
            >
              Chỉnh sửa
            </Button>
          </Space>
        </div>
        <div style={{ clear: 'both' }} />
      </div>

      <Row gutter={[24, 24]}>
        {/* Thông tin đơn hàng */}
        <Col span={16}>
          <Card title="Thông tin đơn hàng" style={{ marginBottom: '24px' }}>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="Mã đơn hàng" span={1}>
                <Text strong>{order.ma_don_hang}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Trạng thái" span={1}>
                <Tag color={statusInfo.color}>{statusInfo.label}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Ngày bán" span={1}>
                {new Date(order.ngay_ban).toLocaleDateString('vi-VN')}
              </Descriptions.Item>
              <Descriptions.Item label="Nguồn đơn hàng" span={1}>
                {order.nguon_don_hang || 'Trực tiếp'}
              </Descriptions.Item>
              <Descriptions.Item label="Người tạo" span={1}>
                {order.nguoi_tao}
              </Descriptions.Item>
              <Descriptions.Item label="Nhân viên bán" span={1}>
                {order.nhan_vien_ban || 'Chưa phân công'}
              </Descriptions.Item>
              <Descriptions.Item label="Ghi chú" span={2}>
                {order.ghi_chu || 'Không có ghi chú'}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* Danh sách sản phẩm */}
          <Card title="Danh sách sản phẩm">
            <Table
              columns={productColumns}
              dataSource={order.san_pham_list || []}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>

        {/* Thông tin khách hàng và thanh toán */}
        <Col span={8}>
          {/* Thông tin khách hàng */}
          <Card title="Thông tin khách hàng" style={{ marginBottom: '24px' }}>
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Tên khách hàng">
                <Text strong>{order.khach_hang?.ho_ten}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="Số điện thoại">
                {order.khach_hang?.so_dien_thoai}
              </Descriptions.Item>
              <Descriptions.Item label="Email">
                {order.khach_hang?.email || 'Chưa có'}
              </Descriptions.Item>
            </Descriptions>
          </Card>

          {/* Thông tin thanh toán */}
          <Card title="Thông tin thanh toán">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Tổng tiền hàng">
                <Text strong>
                  {new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                  }).format(order.tong_tien)}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Chiết khấu">
                <Text>
                  {new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                  }).format(order.chiet_khau || 0)}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Tổng phải trả">
                <Text strong style={{ fontSize: '16px', color: '#1890ff' }}>
                  {new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                  }).format(order.tong_phai_tra)}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Đã thanh toán">
                <Text>
                  {new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                  }).format(order.tong_da_tra || 0)}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="Còn phải trả">
                <Text strong style={{ color: order.con_phai_tra > 0 ? '#ff4d4f' : '#52c41a' }}>
                  {new Intl.NumberFormat('vi-VN', {
                    style: 'currency',
                    currency: 'VND'
                  }).format(order.con_phai_tra || 0)}
                </Text>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default OrderDetail;
