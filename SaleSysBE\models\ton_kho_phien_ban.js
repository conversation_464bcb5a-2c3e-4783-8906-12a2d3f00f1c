'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class TonKhoPhienBan extends Model {
    static associate(models) {
      // Quan hệ với kho hàng
      TonKhoPhienBan.belongsTo(models.KhoHang, {
        foreignKey: 'kho_hang_id',
        as: 'khoHang'
      });

      // Quan hệ với phiên bản sản phẩm
      TonKhoPhienBan.belongsTo(models.PhienBanSanPham, {
        foreignKey: 'phien_ban_san_pham_id',
        as: 'phienBanSanPham'
      });
    }
  }

  TonKhoPhienBan.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    kho_hang_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'kho_hang',
        key: 'id'
      }
    },
    phien_ban_san_pham_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'phien_ban_san_pham',
        key: 'id'
      }
    },
    so_luong_thay_doi: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    so_luong_ton: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0
    },
    so_luong_toi_thieu: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 0,
      comment: 'Số lượng tồn kho tối thiểu'
    },
    gia_von: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: true,
      defaultValue: 0,
      comment: 'Giá vốn của sản phẩm'
    },
    ma_chung_tu: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    },
    ngay_tao: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    },
    ngay_cap_nhap: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW
    }
  }, {
    sequelize,
    modelName: 'TonKhoPhienBan',
    tableName: 'ton_kho_phien_ban',
    timestamps: true,
    createdAt: 'ngay_tao',
    updatedAt: 'ngay_cap_nhap'
  });

  return TonKhoPhienBan;
};
