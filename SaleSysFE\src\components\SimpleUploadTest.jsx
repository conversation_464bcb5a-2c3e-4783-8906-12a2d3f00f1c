import React, { useState } from 'react';
import { Button, message, Card, Image } from 'antd';
import { uploadImage } from '../config/api';

const SimpleUploadTest = () => {
  const [uploading, setUploading] = useState(false);
  const [uploadedImage, setUploadedImage] = useState(null);

  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    console.log('📁 File selected:', file);

    // Validate file
    if (!file.type.startsWith('image/')) {
      message.error('Please select an image file!');
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      message.error('File size must be less than 5MB!');
      return;
    }

    setUploading(true);

    try {
      console.log('🚀 Uploading using API helper...');

      // Use direct fetch to simple endpoint
      const formData = new FormData();
      formData.append('image', file);
      formData.append('folder', 'test');
      formData.append('width', '600');
      formData.append('height', '400');

      const response = await fetch('http://localhost:5000/api/upload/simple', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();

      console.log('✅ Upload success:', data);

      if (data.success) {
        setUploadedImage(data.data);
        message.success('Upload successful!');
      } else {
        throw new Error(data.message || 'Upload failed');
      }

    } catch (error) {
      console.error('❌ Upload error:', error);
      message.error(`Upload failed: ${error.message}`);
    } finally {
      setUploading(false);
    }
  };

  return (
    <Card title="🧪 Simple Upload Test" style={{ maxWidth: 600, margin: '20px auto' }}>
      <div style={{ textAlign: 'center', marginBottom: 20 }}>
        <input
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          style={{ display: 'none' }}
          id="file-input"
        />
        <label htmlFor="file-input">
          <Button 
            type="primary" 
            loading={uploading}
            style={{ cursor: 'pointer' }}
            onClick={() => document.getElementById('file-input').click()}
          >
            {uploading ? 'Uploading...' : 'Select Image to Upload'}
          </Button>
        </label>
      </div>

      {uploadedImage && (
        <div style={{ marginTop: 20 }}>
          <h4>✅ Upload Success!</h4>
          <div style={{ marginBottom: 10 }}>
            <Image
              src={uploadedImage.thumbnail_url || uploadedImage.url}
              alt="Uploaded"
              style={{ maxWidth: 200, maxHeight: 200 }}
            />
          </div>
          <div style={{ background: '#f5f5f5', padding: 10, borderRadius: 4, fontSize: 12 }}>
            <div><strong>URL:</strong> {uploadedImage.url}</div>
            <div><strong>Public ID:</strong> {uploadedImage.public_id}</div>
            <div><strong>Size:</strong> {uploadedImage.width}x{uploadedImage.height}</div>
          </div>
        </div>
      )}

      <div style={{ marginTop: 20, fontSize: 12, color: '#666' }}>
        <p><strong>Debug Info:</strong></p>
        <p>• Check browser console for detailed logs</p>
        <p>• Check Network tab for API calls</p>
        <p>• Backend should be running on port 5000</p>
      </div>
    </Card>
  );
};

export default SimpleUploadTest;
