const { sequelize } = require('./models');

async function resetDatabase() {
  try {
    console.log('🔄 Bắt đầu reset database...');
    
    // Get all tables
    const [tables] = await sequelize.query(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = DATABASE()
    `);
    
    console.log(`📋 Tìm thấy ${tables.length} bảng`);
    
    // Disable foreign key checks
    await sequelize.query('SET FOREIGN_KEY_CHECKS = 0');
    console.log('🔓 Đã tắt foreign key checks');
    
    // Drop all tables
    for (const table of tables) {
      const tableName = table.TABLE_NAME;
      await sequelize.query(`DROP TABLE IF EXISTS \`${tableName}\``);
      console.log(`🗑️  Đã xóa bảng: ${tableName}`);
    }
    
    // Re-enable foreign key checks
    await sequelize.query('SET FOREIGN_KEY_CHECKS = 1');
    console.log('🔒 Đã bật lại foreign key checks');
    
    console.log('✅ Reset database hoàn tất!');
    console.log('📝 Bây giờ hãy chạy: npm run migrate');
    
  } catch (error) {
    console.error('❌ Lỗi reset database:', error.message);
  } finally {
    await sequelize.close();
    process.exit(0);
  }
}

resetDatabase();
