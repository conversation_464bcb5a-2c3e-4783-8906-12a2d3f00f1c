'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class HoSoCaNhan extends Model {
    static associate(models) {
      // Quan hệ với người dùng (1-1)
      HoSoCaNhan.belongsTo(models.NguoiDung, {
        foreignKey: 'nguoi_dung_id',
        as: 'nguoiDung'
      });
    }
  }

  HoSoCaNhan.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    nguoi_dung_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      unique: true,
      references: {
        model: 'nguoi_dung',
        key: 'id'
      }
    },
    ngay_sinh: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    gioi_tinh: {
      type: DataTypes.ENUM('nam', 'nu', 'khac'),
      allowNull: true
    },
    ma_so_thue: {
      type: DataTypes.STRING,
      allowNull: true
    },
    website: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isUrl: true
      }
    },
    mo_ta: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'HoSoCaNhan',
    tableName: 'ho_so_ca_nhan',
    timestamps: false
  });

  return HoSoCaNhan;
};
