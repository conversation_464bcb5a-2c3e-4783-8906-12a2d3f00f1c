import api from '../api';

const tagAPI = {
  getTags: (params = {}) => {
    return api.get('/tags', { params });
  },
  
  getTag: (id) => {
    return api.get(`/tags/${id}`);
  },
  
  createTag: (data) => {
    return api.post('/tags', data);
  },
  
  updateTag: (id, data) => {
    return api.put(`/tags/${id}`, data);
  },
  
  deleteTag: (id) => {
    return api.delete(`/tags/${id}`);
  }
};

export default tagAPI;
