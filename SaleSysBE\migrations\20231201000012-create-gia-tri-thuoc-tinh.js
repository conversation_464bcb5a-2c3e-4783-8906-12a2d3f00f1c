'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('gia_tri_thuoc_tinh', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      thuoc_tinh_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'thuoc_tinh',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      gia_tri: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          notEmpty: true,
          len: [1, 100]
        }
      },
      nguoi_tao: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nguoi_cap_nhap: {
        type: Sequelize.STRING,
        allowNull: true
      },
      ngay_tao: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      ngay_cap_nhap: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('gia_tri_thuoc_tinh', ['thuoc_tinh_id']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('gia_tri_thuoc_tinh');
  }
};
