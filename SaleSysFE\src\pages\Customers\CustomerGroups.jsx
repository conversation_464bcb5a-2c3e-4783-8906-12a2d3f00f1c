import React, { useEffect, useState } from 'react';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { PageHeader, DataTable } from '../../components/Common';
import { Form, Input, Modal , Select, message, Popconfirm } from 'antd';
import { customerAPI } from '../../services/api';
import dayjs from 'dayjs';



const CustomerGroups = () => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [openModal, setOpenModal] = useState(false);
  const [form] = Form.useForm();
  const [openEditModal, setOpenEditModal] = useState(false);
  const [editForm] = Form.useForm();
  const [editingRecord, setEditingRecord] = useState(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  useEffect(() => {
    fetchDataTable(pagination.current, pagination.pageSize);
    // eslint-disable-next-line
  }, []);

  const fetchDataTable = async (current = pagination.current, pageSize = pagination.pageSize) => {
    setLoading(true);
    try {
      const res = await customerAPI.getAllCustomerGroup({ current, pageSize });
      setData(res.data?.data || []);
      setPagination(res.data?.pagination);
    } catch (error) {
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = async () => {
    try {
      const values = await form.validateFields();
      const res = await customerAPI.createCustomerGroup(values);
      if (res.data?.success === true) {
        message.success(res.data?.message);
      } else {
        message.error(res.data?.message);
      }
      setOpenModal(false);
      form.resetFields();
      fetchDataTable();
    } catch (error) {
    }
  };

  const handleEdit = (record) => {
    setEditingRecord(record);
    editForm.setFieldsValue(record);
    setOpenEditModal(true);
  };

  const handleDelete = async (record) => {
    try {
      const res = await customerAPI.deleteCustomerGroup(record.id);
      if (res.data?.success === true) {
        message.success(res.data?.message);
      } else {
        message.error(res.data?.message);
      }
      fetchDataTable();
    } catch (error) {}
  };

  const handleUpdate = async () => {
    try {
      const values = await editForm.validateFields();
      const res = await customerAPI.updateCustomerGroup(editingRecord.id, values);
      if (res.data?.success === true) {
        message.success(res.data?.message);
      } else {
        message.error(res.data?.message);
      }
      setOpenEditModal(false);
      setEditingRecord(null);
      editForm.resetFields();
      fetchDataTable();
    } catch (error) {}
  };

  const handleTableChange = (current, pageSize) => {
    fetchDataTable(current, pageSize);
  };

  const columns = [
    {
      title: 'STT',
      key: 'stt',
      align: 'center',
      width: 70,
      render: (text, record, index) => index + 1,
    },
    {
      title: 'Tên nhóm',
      dataIndex: 'ten_nhom',
      key: 'ten_nhom',
      sorter: true,
    },
    {
      title: 'Mã nhóm',
      dataIndex: 'ma_nhom',
      key: 'ma_nhom',
    },
    {
      title: 'Loại nhóm',
      dataIndex: 'loai_nhom',
      key: 'loai_nhom',
      render: (value) => {
        if (value === 1) return "Cố định";
        if (value === 2) return "Tự động";
        return "";
      }
    },
    {
      title: 'Mô tả',
      dataIndex: 'mo_ta',
      key: 'mo_ta',
    },
    {
      title: 'Số lượng khách hàng',
      dataIndex: 'so_luong_khach_hang',
      key: 'so_luong_khach_hang',
      align: 'left',
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'ngay_tao',
      key: 'ngay_tao',
      render: (value) => value ? dayjs(value).format('DD/MM/YYYY') : '',
    },
    {
      title: 'Thao tác',
      key: 'actions',
      align: 'center',
      width: 100,
      render: (text, record) => (
        <span>
          <EditOutlined style={{ color: '#1890ff', marginRight: 16, cursor: 'pointer' }} onClick={() => handleEdit(record)} />
          <Popconfirm
            title="Bạn có chắc chắn muốn xóa nhóm khách hàng này?"
            onConfirm={() => handleDelete(record)}
            okText="Xóa"
            cancelText="Hủy"
            okButtonProps={{ danger: true }}
          >
            <DeleteOutlined style={{ color: '#ff4d4f', cursor: 'pointer' }} />
          </Popconfirm>
        </span>
      ),
    },
  ];
  return (
    <div>
      <PageHeader
        title="Nhóm khách hàng"
        actions={[
          {
            type: 'primary',
            icon: <PlusOutlined />,
            label: 'Thêm nhóm khách hàng',
            onClick: () => setOpenModal(true),
          },
        ]}
      />
      <DataTable
        columns={columns}
        data={data}
        rowKey="id"
        bordered
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          onChange: handleTableChange,
        }}
        size="middle"
        showSearch={false}
        loading={loading}
      />

      <Modal
        title="Thêm nhóm khách hàng"
        open={openModal}
        onCancel={() => setOpenModal(false)}
        onOk={handleAdd}
        okText="Lưu"
        cancelText="Hủy"
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="Tên nhóm"
            name="ten_nhom"
            rules={[{ required: true, message: 'Vui lòng nhập tên nhóm!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="Mã nhóm"
            name="ma_nhom"
            rules={[{ required: true, message: 'Vui lòng nhập mã nhóm!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="Loại nhóm"
            name="loai_nhom"
            rules={[{ required: true, message: 'Vui lòng chọn loại nhóm!' }]}
          >
            <Select>
              <Select.Option value={1}>Cố định</Select.Option>
              <Select.Option value={2}>Tự động</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="Mô tả" name="mo_ta">
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="Sửa nhóm khách hàng"
        open={openEditModal}
        onCancel={() => { setOpenEditModal(false); setEditingRecord(null); }}
        onOk={handleUpdate}
        okText="Lưu"
        cancelText="Hủy"
      >
        <Form form={editForm} layout="vertical">
          <Form.Item
            label="Tên nhóm"
            name="ten_nhom"
            rules={[{ required: true, message: 'Vui lòng nhập tên nhóm!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="Mã nhóm"
            name="ma_nhom"
            rules={[{ required: true, message: 'Vui lòng nhập mã nhóm!' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label="Loại nhóm"
            name="loai_nhom"
            rules={[{ required: true, message: 'Vui lòng chọn loại nhóm!' }]}
          >
            <Select>
              <Select.Option value={1}>Cố định</Select.Option>
              <Select.Option value={2}>Tự động</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="Mô tả" name="mo_ta">
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CustomerGroups;
