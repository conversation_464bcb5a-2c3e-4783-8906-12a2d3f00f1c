const { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, SanPham, Nguoi<PERSON>ung, TonKhoPhien<PERSON>an, <PERSON><PERSON>ch<PERSON><PERSON> } = require('../models');
const { Op } = require('sequelize');
const { AppError } = require('../middleware/errorHandler');

// L<PERSON>y danh sách đơn hàng
const getOrders = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      trang_thai,
      tu_ngay,
      den_ngay,
      khach_hang_id
    } = req.query;

    const offset = (page - 1) * limit;
    let whereCondition = {};

    console.log('🔧 getOrders params:', { page, limit, search, trang_thai, tu_ngay, den_ngay });

    // Tìm kiếm theo mã đơn hàng và ghi chú
    if (search) {
      whereCondition[Op.or] = [
        { ma_don_hang: { [Op.like]: `%${search}%` } },
        { ghi_chu: { [Op.like]: `%${search}%` } }
      ];
    }

    // Lọc theo trạng thái
    if (trang_thai) {
      whereCondition.trang_thai = trang_thai;
    }

    // Lọc theo khách hàng
    if (khach_hang_id) {
      whereCondition.khach_hang_id = khach_hang_id;
    }

    // Lọc theo ngày - kiểm tra valid date
    if (tu_ngay && tu_ngay !== 'undefined' && den_ngay && den_ngay !== 'undefined') {
      const startDate = new Date(tu_ngay);
      const endDate = new Date(den_ngay);
      if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
        whereCondition.ngay_ban = {
          [Op.between]: [startDate, endDate]
        };
      }
    } else if (tu_ngay && tu_ngay !== 'undefined') {
      const startDate = new Date(tu_ngay);
      if (!isNaN(startDate.getTime())) {
        whereCondition.ngay_ban = {
          [Op.gte]: startDate
        };
      }
    } else if (den_ngay && den_ngay !== 'undefined') {
      const endDate = new Date(den_ngay);
      if (!isNaN(endDate.getTime())) {
        whereCondition.ngay_ban = {
          [Op.lte]: endDate
        };
      }
    }

    // Query với thông tin khách hàng và sản phẩm
    const { count, rows: orders } = await DonHang.findAndCountAll({
      where: whereCondition,
      include: [
        {
          model: NguoiDung,
          as: 'khachHang',
          attributes: ['ho_ten', 'so_dien_thoai'],
          required: false
        },
        {
          model: DonHangSanPham,
          as: 'sanPhamList',
          required: false
        }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['id', 'DESC']]
    });

    // Tính toán thống kê
    const stats = await calculateOrderStats();

    // Format dữ liệu trả về với thông tin thật
    const formattedOrders = orders.map(order => ({
      id: order.id,
      ma_don_hang: order.ma_don_hang,
      ten_khach_hang: order.khachHang?.ho_ten || 'Khách lẻ',
      so_dien_thoai: order.khachHang?.so_dien_thoai,
      ngay_ban: order.ngay_ban,
      trang_thai: order.trang_thai,
      tong_tien: order.tong_tien,
      chiet_khau: order.chiet_khau,
      tong_phai_tra: order.tong_phai_tra,
      ghi_chu: order.ghi_chu,
      so_luong_san_pham: order.sanPhamList?.length || 0,
      nguoi_tao: order.nguoi_tao
    }));

    res.json({
      success: true,
      data: formattedOrders,
      total: count,
      stats,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('Error getting orders:', error);
    res.status(500).json({
      success: false,
      message: 'Có lỗi xảy ra khi lấy danh sách đơn hàng'
    });
  }
};

// Lấy chi tiết đơn hàng
const getOrder = async (req, res) => {
  try {
    const { id } = req.params;

    const order = await DonHang.findByPk(id, {
      include: [
        {
          model: NguoiDung,
          as: 'khachHang',
          attributes: ['ho_ten', 'so_dien_thoai', 'email'],
          required: false
        },
        {
          model: DonHangSanPham,
          as: 'sanPhamList',
          include: [{
            model: PhienBanSanPham,
            as: 'phienBanSanPham',
            include: [{
              model: SanPham,
              as: 'sanPham',
              attributes: ['ten']
            }]
          }],
          required: false
        },
        {
          model: NguoiDung,
          as: 'nhanVienBan',
          attributes: ['ho_ten', 'email'],
          required: false
        }
      ]
    });

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy đơn hàng'
      });
    }

    // Format dữ liệu chi tiết
    const formattedOrder = {
      id: order.id,
      ma_don_hang: order.ma_don_hang,
      khach_hang: {
        ho_ten: order.khachHang?.ho_ten || 'Khách lẻ',
        so_dien_thoai: order.khachHang?.so_dien_thoai,
        email: order.khachHang?.email
      },
      ngay_ban: order.ngay_ban,
      trang_thai: order.trang_thai,
      nguon_don_hang: order.nguon_don_hang,
      tong_tien: order.tong_tien,
      chiet_khau: order.chiet_khau,
      tong_phai_tra: order.tong_phai_tra,
      tong_da_tra: order.tong_da_tra,
      con_phai_tra: order.con_phai_tra,
      ghi_chu: order.ghi_chu,
      nguoi_tao: order.nguoi_tao,
      nhan_vien_ban: order.nhanVienBan?.ho_ten,
      san_pham_list: order.sanPhamList?.map(item => ({
        id: item.id,
        ten_san_pham: item.ten_san_pham || item.phienBanSanPham?.sanPham?.ten,
        so_luong: item.so_luong,
        don_gia: item.don_gia,
        thanh_tien: item.thanh_tien,
        phien_ban_san_pham_id: item.phien_ban_san_pham_id
      })) || []
    };

    res.json({
      success: true,
      data: formattedOrder
    });
  } catch (error) {
    console.error('Error getting order:', error);
    res.status(500).json({
      success: false,
      message: 'Có lỗi xảy ra khi lấy thông tin đơn hàng'
    });
  }
};

// Tạo đơn hàng mới
const createOrder = async (req, res) => {
  const transaction = await DonHang.sequelize.transaction();

  try {
    const {
      khach_hang_id,
      ten_khach_hang,
      so_dien_thoai,
      dia_chi,
      ngay_dat_hang,
      trang_thai = 'cho_xu_ly',
      ghi_chu,
      phuong_thuc_giao_hang,
      chi_tiet_don_hang,
      tong_tien_hang,
      giam_gia = 0,
      tong_thanh_toan,
      loai_giam_gia = 'amount'
    } = req.body;

    console.log('📋 Creating order with data:', req.body);

    // Validate chi tiết đơn hàng
    if (!chi_tiet_don_hang || chi_tiet_don_hang.length === 0) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        message: 'Đơn hàng phải có ít nhất một sản phẩm'
      });
    }

    // Validate khách hàng - cho phép khách lẻ (không cần khach_hang_id)
    // if (!khach_hang_id && !ten_khach_hang) {
    //   await transaction.rollback();
    //   return res.status(400).json({
    //     success: false,
    //     message: 'Vui lòng nhập thông tin khách hàng'
    //   });
    // }

    // Tạo mã đơn hàng
    const ma_don_hang = await generateOrderCode();

    // Tính tổng tiền từ chi tiết đơn hàng
    let calculated_total = 0;
    for (const item of chi_tiet_don_hang) {
      calculated_total += item.so_luong * item.gia_ban;
    }

    // Tính giảm giá
    const chiet_khau_amount = loai_giam_gia === 'percent'
      ? (calculated_total * giam_gia / 100)
      : giam_gia;

    const tong_phai_tra = calculated_total - chiet_khau_amount;

    // Tạo đơn hàng với field mapping đúng theo model
    const order = await DonHang.create({
      ma_don_hang,
      khach_hang_id: khach_hang_id || null, // Cho phép null cho khách lẻ
      ngay_ban: ngay_dat_hang || new Date(),
      trang_thai,
      nguon_don_hang: phuong_thuc_giao_hang || 'web', // Lưu delivery method vào nguon_don_hang
      tong_tien: calculated_total,
      chiet_khau: chiet_khau_amount,
      tong_phai_tra,
      tong_da_tra: 0,
      con_phai_tra: tong_phai_tra,
      ghi_chu,
      nhan_vien_ban_id: null, // Tạm thời để null cho test API
      nguoi_tao: req.user?.username || 'test'
    }, { transaction });

    console.log('📋 Order created with ID:', order.id);

    // Tạo địa chỉ giao hàng nếu có thông tin
    if (ten_khach_hang || so_dien_thoai || dia_chi) {
      const { DonHangDiaChiGiao } = require('../models');
      await DonHangDiaChiGiao.create({
        don_hang_id: order.id,
        dia_chi: dia_chi || '',
        nguoi_nhan: ten_khach_hang || 'Khách lẻ',
        so_dien_thoai_nhan: so_dien_thoai || null
      }, { transaction });

      console.log('📋 Delivery address created');
    }

    // Tạo chi tiết đơn hàng
    for (const item of chi_tiet_don_hang) {
      // Lấy thông tin sản phẩm để có tên đúng
      const { PhienBanSanPham, SanPham } = require('../models');
      const variant = await PhienBanSanPham.findByPk(item.phien_ban_san_pham_id, {
        include: [{
          model: SanPham,
          as: 'sanPham',
          attributes: ['ten']
        }]
      });

      const productName = variant ?
        `${variant.sanPham?.ten} - ${variant.ten_phien_ban}` :
        `Sản phẩm ${item.phien_ban_san_pham_id}`;

      await DonHangSanPham.create({
        don_hang_id: order.id,
        phien_ban_san_pham_id: item.phien_ban_san_pham_id,
        ten_san_pham: productName,
        so_luong: item.so_luong,
        don_gia: item.gia_ban,
        thanh_tien: item.so_luong * item.gia_ban
      }, { transaction });

      console.log('📋 Order item created for variant:', item.phien_ban_san_pham_id);

      // Cập nhật tồn kho (trừ tồn kho) chỉ khi đơn hàng được xác nhận
      if (trang_thai === 'da_xac_nhan' || trang_thai === 'hoan_thanh') {
        await updateInventoryForOrder(item.phien_ban_san_pham_id, -item.so_luong, transaction);
      }
    }

    // 🟩 LOGIC CÔNG NỢ MỚI: Chỉ tính công nợ khi đơn hàng được xác nhận
    // ❌ Không tính công nợ cho: cho_xu_ly (draft), huy (cancelled)
    // ✅ Tính công nợ cho: da_xac_nhan, da_dong_goi, da_giao, hoan_thanh
    const shouldCreateDebt = ['da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh'].includes(trang_thai);

    if (khach_hang_id && tong_phai_tra > 0 && shouldCreateDebt) {
      console.log(`🟩 Creating debt for confirmed order. Status: ${trang_thai}, Amount: ${tong_phai_tra}`);
      await updateCustomerDebt(khach_hang_id, tong_phai_tra, transaction);
    } else if (khach_hang_id && tong_phai_tra > 0 && !shouldCreateDebt) {
      console.log(`⚠️ Order created but debt not recorded. Status: ${trang_thai} (waiting for confirmation)`);
    }

    await transaction.commit();
    console.log('📋 Transaction committed successfully');

    // Trả về thông tin đơn hàng vừa tạo
    res.status(201).json({
      success: true,
      message: 'Tạo đơn hàng thành công',
      data: {
        id: order.id,
        ma_don_hang: order.ma_don_hang,
        khach_hang_id: order.khach_hang_id,
        trang_thai: order.trang_thai,
        tong_tien: order.tong_tien,
        tong_phai_tra: order.tong_phai_tra,
        so_luong_san_pham: chi_tiet_don_hang.length
      }
    });

  } catch (error) {
    await transaction.rollback();
    console.error('❌ Error creating order:', error);
    res.status(500).json({
      success: false,
      message: 'Có lỗi xảy ra khi tạo đơn hàng',
      error: error.message
    });
  }
};

// Cập nhật đơn hàng
const updateOrder = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      ngay_cap_nhap: new Date()
    };

    const [updatedRowsCount] = await DonHang.update(updateData, {
      where: { id }
    });

    if (updatedRowsCount === 0) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy đơn hàng'
      });
    }

    // Lấy đơn hàng đã cập nhật
    const updatedOrder = await DonHang.findByPk(id);

    res.json({
      success: true,
      message: 'Cập nhật đơn hàng thành công',
      data: updatedOrder
    });
  } catch (error) {
    console.error('Error updating order:', error);
    res.status(500).json({
      success: false,
      message: 'Có lỗi xảy ra khi cập nhật đơn hàng'
    });
  }
};

// Helper functions
const generateOrderCode = async () => {
  const today = new Date();
  const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
  
  const lastOrder = await DonHang.findOne({
    where: {
      ma_don_hang: {
        [Op.like]: `DH${dateStr}%`
      }
    },
    order: [['ma_don_hang', 'DESC']]
  });

  let sequence = 1;
  if (lastOrder) {
    const lastSequence = parseInt(lastOrder.ma_don_hang.slice(-4));
    sequence = lastSequence + 1;
  }

  return `DH${dateStr}${sequence.toString().padStart(4, '0')}`;
};

const calculateOrderStats = async () => {
  const today = new Date();
  const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

  const [totalOrders, pendingOrders, completedOrders, totalRevenue] = await Promise.all([
    DonHang.count(),
    DonHang.count({ where: { trang_thai: 'cho_xu_ly' } }), // Sửa từ cho_xu_ly thành cho_xu_ly
    DonHang.count({ where: { trang_thai: 'hoan_thanh' } }),
    DonHang.sum('tong_phai_tra', { // Sửa từ tong_thanh_toan thành tong_phai_tra
      where: {
        trang_thai: 'hoan_thanh',
        ngay_ban: { [Op.gte]: startOfMonth } // Sửa từ ngay_dat_hang thành ngay_ban
      }
    })
  ]);

  return {
    total_orders: totalOrders || 0,
    pending_orders: pendingOrders || 0,
    completed_orders: completedOrders || 0,
    total_revenue: totalRevenue || 0
  };
};

const updateInventoryForOrder = async (variantId, quantity, transaction) => {
  // Cập nhật tồn kho cho phiên bản sản phẩm
  const inventory = await TonKhoPhienBan.findOne({
    where: { phien_ban_san_pham_id: variantId }
  });

  if (inventory) {
    await inventory.update({
      so_luong_ton: inventory.so_luong_ton + quantity
    }, { transaction });
  }
};

// Cập nhật công nợ khách hàng khi tạo đơn hàng
const updateCustomerDebt = async (customerId, orderAmount, transaction) => {
  try {
    const { CongNoNguoiDung } = require('../models');

    console.log(`💰 Updating debt for customer ${customerId}, amount: ${orderAmount}`);

    // Tìm hoặc tạo bản ghi công nợ
    let debtRecord = await CongNoNguoiDung.findOne({
      where: { nguoi_dung_id: customerId }
    });

    if (!debtRecord) {
      // Tạo mới nếu chưa có
      debtRecord = await CongNoNguoiDung.create({
        nguoi_dung_id: customerId,
        tong_cong_no: orderAmount,
        ghi_chu: 'Tạo từ đơn hàng'
      }, { transaction });
      console.log(`💰 Created new debt record: ${orderAmount} VND`);
    } else {
      // Cập nhật công nợ hiện tại
      const currentDebt = parseFloat(debtRecord.tong_cong_no || 0);
      const newDebt = currentDebt + orderAmount;

      await debtRecord.update({
        tong_cong_no: newDebt
      }, { transaction });

      console.log(`💰 Updated debt: ${currentDebt} -> ${newDebt} VND`);
    }
  } catch (error) {
    console.error('❌ Error updating customer debt:', error);
    // Không throw error để không làm fail transaction tạo đơn hàng
  }
};

module.exports = {
  getOrders,
  getOrder,
  createOrder,
  updateOrder
};
