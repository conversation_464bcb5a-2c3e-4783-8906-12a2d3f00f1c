import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![vertical-align-bottom](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1OS45IDc4MEgxNjQuMWMtNC41IDAtOC4xIDMuNi04LjEgOHY2MGMwIDQuNCAzLjYgOCA4LjEgOGg2OTUuOGM0LjUgMCA4LjEtMy42IDguMS04di02MGMwLTQuNC0zLjYtOC04LjEtOHpNNTA1LjcgNjY5YTggOCAwIDAwMTIuNiAwbDExMi0xNDEuN2M0LjEtNS4yLjQtMTIuOS02LjMtMTIuOWgtNzQuMVYxNzZjMC00LjQtMy42LTgtOC04aC02MGMtNC40IDAtOCAzLjYtOCA4djMzOC4zSDQwMGMtNi43IDAtMTAuNCA3LjctNi4zIDEyLjlsMTEyIDE0MS44eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
