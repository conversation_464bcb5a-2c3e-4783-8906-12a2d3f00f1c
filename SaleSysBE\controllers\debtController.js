const { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } = require('../models');
const { Op } = require('sequelize');

// L<PERSON>y danh sách công nợ khách hàng
const getDebtList = async (req, res) => {
  try {
    const { 
      search = '', 
      status = '', 
      start_date, 
      end_date, 
      page = 1, 
      limit = 20 
    } = req.query;

    console.log('📊 Getting debt list with filters:', req.query);

    // Xây dựng điều kiện where
    const whereConditions = {};
    
    if (search) {
      whereConditions[Op.or] = [
        { ho_ten: { [Op.like]: `%${search}%` } },
        { so_dien_thoai: { [Op.like]: `%${search}%` } }
      ];
    }

    // Lấy danh sách khách hàng có đơn hàng
    const customers = await NguoiDung.findAll({
      where: whereConditions,
      include: [
        {
          model: <PERSON><PERSON><PERSON>,
          as: 'don<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
          attributes: ['id', 'tong_phai_tra', 'tong_da_tra', 'ngay_ban', 'trang_thai'],
          required: true, // Chỉ lấy khách hàng có đơn hàng
          where: start_date || end_date ? {
            ...(start_date && { ngay_ban: { [Op.gte]: start_date } }),
            ...(end_date && { ngay_ban: { [Op.lte]: end_date } })
          } : undefined
        }
      ],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    });

    // Đếm tổng số khách hàng (đơn giản hóa)
    const totalCount = customers.length;

    // Tính toán công nợ cho từng khách hàng
    const debtData = customers.map(customer => {
      const orders = customer.donHangKhachHang || [];
      
      const totalDebt = orders.reduce((sum, order) => {
        return sum + ((order.tong_phai_tra || 0) - (order.tong_da_tra || 0));
      }, 0);

      const overdueDebt = orders.reduce((sum, order) => {
        const daysDiff = Math.floor((new Date() - new Date(order.ngay_ban)) / (1000 * 60 * 60 * 24));
        if (daysDiff > 30 && ((order.tong_phai_tra || 0) - (order.tong_da_tra || 0)) > 0) {
          return sum + ((order.tong_phai_tra || 0) - (order.tong_da_tra || 0));
        }
        return sum;
      }, 0);

      // Xác định trạng thái
      let customerStatus = 'normal';
      if (overdueDebt > 0) {
        customerStatus = 'overdue';
      } else if (totalDebt > 5000000) { // Cảnh báo nếu nợ > 5 triệu
        customerStatus = 'warning';
      }

      // Xác định nhóm khách hàng (giả lập dựa trên tổng mua)
      const totalPurchased = orders.reduce((sum, order) => sum + (order.tong_phai_tra || 0), 0);
      let customerGroup = 'retail';
      if (totalPurchased > 50000000) customerGroup = 'vip';
      else if (totalPurchased > 20000000) customerGroup = 'agency';
      else if (totalPurchased > 10000000) customerGroup = 'wholesale';

      return {
        customer_id: customer.id,
        customer_name: customer.ho_ten,
        customer_phone: customer.so_dien_thoai,
        customer_email: customer.email,
        customer_group: customerGroup,
        total_debt: totalDebt,
        overdue_debt: overdueDebt,
        order_count: orders.length,
        last_updated: orders.length > 0 ?
          Math.max(...orders.map(o => new Date(o.ngay_ban))) :
          customer.createdAt,
        status: customerStatus,
        total_purchased: totalPurchased
      };
    });

    // Lọc theo trạng thái nếu có
    const filteredData = status ? 
      debtData.filter(item => item.status === status) : 
      debtData;

    // Tính thống kê tổng quan
    const stats = {
      total_debt: debtData.reduce((sum, item) => sum + item.total_debt, 0),
      overdue_debt: debtData.reduce((sum, item) => sum + item.overdue_debt, 0),
      customers_with_debt: debtData.filter(item => item.total_debt > 0).length,
      overdue_customers: debtData.filter(item => item.overdue_debt > 0).length
    };

    console.log('✅ Debt list retrieved successfully');
    res.json({
      success: true,
      data: filteredData,
      stats,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('❌ Error in getDebtList:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy danh sách công nợ',
      error: error.message
    });
  }
};

// Lấy chi tiết công nợ khách hàng
const getCustomerDebtDetail = async (req, res) => {
  try {
    const { customerId } = req.params;
    console.log(`📊 Getting debt detail for customer: ${customerId}`);

    const customer = await NguoiDung.findByPk(customerId, {
      include: [
        {
          model: DonHang,
          as: 'donHangKhachHang',
          include: [
            {
              model: DonHangSanPham,
              as: 'sanPhamList',
              attributes: ['ten_san_pham', 'so_luong', 'don_gia']
            }
          ]
        }
      ]
    });

    if (!customer) {
      return res.status(404).json({
        success: false,
        message: 'Không tìm thấy khách hàng'
      });
    }

    // Tính toán chi tiết công nợ
    const orders = customer.donHangKhachHang || [];
    const totalDebt = orders.reduce((sum, order) => {
      return sum + ((order.tong_phai_tra || 0) - (order.tong_da_tra || 0));
    }, 0);

    const totalPurchased = orders.reduce((sum, order) => {
      return sum + (order.tong_phai_tra || 0);
    }, 0);

    // Format dữ liệu đơn hàng
    const orderData = orders.map(order => ({
      order_id: order.id,
      order_code: order.ma_don_hang,
      order_date: order.ngay_ban,
      total_amount: order.tong_phai_tra,
      paid_amount: order.tong_da_tra || 0,
      debt_amount: (order.tong_phai_tra || 0) - (order.tong_da_tra || 0),
      payment_status: (order.tong_da_tra || 0) >= (order.tong_phai_tra || 0) ? 'paid' : 
                     (order.tong_da_tra || 0) > 0 ? 'partial' : 'unpaid'
    }));

    // Tạo timeline (giả lập)
    const timeline = orders.map(order => ({
      type: 'order',
      title: `Đơn hàng ${order.ma_don_hang}`,
      date: order.ngay_ban,
      description: `Tổng tiền: ${new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(order.tong_phai_tra)}`
    }));

    const customerDetail = {
      customer_id: customer.id,
      customer_name: customer.ho_ten,
      customer_phone: customer.so_dien_thoai,
      customer_email: customer.email,
      customer_address: customer.dia_chi,
      created_date: customer.createdAt,
      last_order_date: orders.length > 0 ? 
        Math.max(...orders.map(o => new Date(o.ngay_ban))) : null,
      total_debt: totalDebt,
      total_purchased: totalPurchased,
      orders: orderData,
      payments: [], // TODO: Implement payment history
      timeline: timeline.sort((a, b) => new Date(b.date) - new Date(a.date))
    };

    console.log('✅ Customer debt detail retrieved successfully');
    res.json({
      success: true,
      data: customerDetail
    });

  } catch (error) {
    console.error('❌ Error in getCustomerDebtDetail:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy chi tiết công nợ',
      error: error.message
    });
  }
};

// Tạo phiếu thu/chi
const createPayment = async (req, res) => {
  try {
    const { customer_id, type, amount, payment_method, note } = req.body;
    
    console.log('💰 Creating payment:', req.body);

    // Validate input
    if (!customer_id || !type || !amount) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin bắt buộc'
      });
    }

    // Tạo phiếu thu/chi (giả lập - cần tạo model PhieuThuChi)
    const payment = {
      id: Date.now(),
      customer_id,
      type,
      amount,
      payment_method: payment_method || 'Tiền mặt',
      note,
      created_by: req.user?.username || 'test',
      created_at: new Date()
    };

    // TODO: Lưu vào database và cập nhật công nợ

    console.log('✅ Payment created successfully');
    res.status(201).json({
      success: true,
      message: 'Tạo phiếu thu/chi thành công',
      data: payment
    });

  } catch (error) {
    console.error('❌ Error in createPayment:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi tạo phiếu thu/chi',
      error: error.message
    });
  }
};

// Lấy báo cáo công nợ
const getDebtReport = async (req, res) => {
  try {
    const { start_date, end_date, type = 'overview' } = req.query;
    
    console.log('📊 Getting debt report:', req.query);

    // Dữ liệu mẫu cho báo cáo
    const reportData = {
      stats: {
        totalDebt: 150000000,
        overdueDebt: 45000000,
        collectionRate: 75,
        avgDebtPerCustomer: 2500000
      },
      chartData: [
        { month: 'T1', debt: 50000000, paid: 30000000 },
        { month: 'T2', debt: 45000000, paid: 35000000 },
        { month: 'T3', debt: 60000000, paid: 25000000 },
        { month: 'T4', debt: 55000000, paid: 40000000 },
        { month: 'T5', debt: 70000000, paid: 45000000 },
        { month: 'T6', debt: 65000000, paid: 50000000 }
      ],
      pieData: [
        { name: 'Đã thanh toán', value: 60, color: '#52c41a' },
        { name: 'Chưa thanh toán', value: 25, color: '#ff4d4f' },
        { name: 'Quá hạn', value: 15, color: '#ff7875' }
      ],
      topDebtors: [
        { rank: 1, customer: 'Nguyễn Văn A', debt: 15000000, orders: 5 },
        { rank: 2, customer: 'Trần Thị B', debt: 12000000, orders: 3 },
        { rank: 3, customer: 'Lê Văn C', debt: 10000000, orders: 4 },
        { rank: 4, customer: 'Phạm Thị D', debt: 8000000, orders: 2 },
        { rank: 5, customer: 'Hoàng Văn E', debt: 7000000, orders: 6 }
      ]
    };

    console.log('✅ Debt report retrieved successfully');
    res.json({
      success: true,
      data: reportData
    });

  } catch (error) {
    console.error('❌ Error in getDebtReport:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy báo cáo công nợ',
      error: error.message
    });
  }
};

// Tạo ghi chú nội bộ
const createInternalNote = async (req, res) => {
  try {
    const { customer_id, content, type } = req.body;

    console.log('📝 Creating internal note:', req.body);

    // Validate input
    if (!customer_id || !content || !type) {
      return res.status(400).json({
        success: false,
        message: 'Thiếu thông tin bắt buộc'
      });
    }

    // Tạo ghi chú nội bộ (giả lập - cần tạo model InternalNote)
    const note = {
      id: Date.now(),
      customer_id,
      content,
      type,
      created_by: req.user?.username || 'test',
      created_at: new Date()
    };

    // TODO: Lưu vào database

    console.log('✅ Internal note created successfully');
    res.status(201).json({
      success: true,
      message: 'Tạo ghi chú nội bộ thành công',
      data: note
    });

  } catch (error) {
    console.error('❌ Error in createInternalNote:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi tạo ghi chú nội bộ',
      error: error.message
    });
  }
};

// Lấy danh sách ghi chú nội bộ
const getInternalNotes = async (req, res) => {
  try {
    const { customerId } = req.params;

    console.log(`📝 Getting internal notes for customer: ${customerId}`);

    // Dữ liệu mẫu ghi chú nội bộ
    const notes = [
      {
        id: 1,
        content: 'Khách hàng đã hẹn thanh toán vào cuối tháng',
        created_by: 'Nguyễn Văn A',
        created_at: '2025-07-01 10:30',
        type: 'reminder'
      },
      {
        id: 2,
        content: 'Đã gọi điện nhắc nhở, khách hàng cam kết thanh toán trong tuần',
        created_by: 'Trần Thị B',
        created_at: '2025-07-05 14:20',
        type: 'contact'
      }
    ];

    console.log('✅ Internal notes retrieved successfully');
    res.json({
      success: true,
      data: notes
    });

  } catch (error) {
    console.error('❌ Error in getInternalNotes:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server khi lấy ghi chú nội bộ',
      error: error.message
    });
  }
};

module.exports = {
  getDebtList,
  getCustomerDebtDetail,
  createPayment,
  getDebtReport,
  createInternalNote,
  getInternalNotes
};
