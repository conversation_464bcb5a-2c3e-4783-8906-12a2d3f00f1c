'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('nhom_khach_hang_nguoi_dung', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      nguoi_dung_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      nhom_khach_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'nhom_khach_hang',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      }
    });

    // Add indexes
    await queryInterface.addIndex('nhom_khach_hang_nguoi_dung', ['nguoi_dung_id']);
    await queryInterface.addIndex('nhom_khach_hang_nguoi_dung', ['nhom_khach_hang_id']);
    
    // Add unique constraint for combination
    await queryInterface.addIndex('nhom_khach_hang_nguoi_dung', ['nguoi_dung_id', 'nhom_khach_hang_id'], {
      unique: true,
      name: 'nhom_kh_nd_unique'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('nhom_khach_hang_nguoi_dung');
  }
};
