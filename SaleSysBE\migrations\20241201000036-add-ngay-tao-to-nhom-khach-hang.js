'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Thêm cột ngay_tao
    await queryInterface.addColumn('nhom_khach_hang', 'ngay_tao', {
      type: Sequelize.DATE,
      allowNull: false,
      defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
    });
    
    // Thêm cột loai_nhom
    await queryInterface.addColumn('nhom_khach_hang', 'loai_nhom', {
      type: Sequelize.INTEGER,
      allowNull: true,
      defaultValue: 1,
      comment: 'Loại nhóm khách hàng'
    });
  },

  async down(queryInterface, Sequelize) {
    // Kiểm tra sự tồn tại của cột trước khi xóa
    try {
      // Xóa cột ngay_tao nếu tồn tại
      await queryInterface.removeColumn('nhom_khach_hang', 'ngay_tao');
    } catch (error) {
      console.log('Cột ngay_tao không tồn tại hoặc đã bị xóa');
    }
    
    try {
      // Xóa cột loai_nhom nếu tồn tại
      await queryInterface.removeColumn('nhom_khach_hang', 'loai_nhom');
    } catch (error) {
      console.log('Cột loai_nhom không tồn tại hoặc đã bị xóa');
    }
  }
};

