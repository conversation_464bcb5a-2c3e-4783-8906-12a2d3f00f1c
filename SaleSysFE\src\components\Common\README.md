# Common Components

Th<PERSON> viện các component chung được sử dụng trong toàn bộ ứng dụng.

## DataTable

Component bảng dữ liệu với đầy đủ tính năng.

### Props

- `data`: <PERSON><PERSON>ng dữ liệu hiển thị
- `columns`: <PERSON><PERSON><PERSON> hình cột
- `loading`: Trạng thái loading
- `pagination`: <PERSON><PERSON>u hình phân trang
- `onEdit`, `onDelete`, `onView`: Callback cho các thao tác
- `rowSelection`: C<PERSON>u hình chọn dòng
- `actions`: Thao tác tùy chỉnh

### Ví dụ sử dụng

```jsx
import { DataTable } from '../../components/Common';

const columns = [
  {
    title: 'Tên',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: 'Email',
    dataIndex: 'email',
    key: 'email'
  }
];

<DataTable
  data={users}
  columns={columns}
  loading={loading}
  onEdit={handleEdit}
  onDelete={handleDelete}
  pagination={{
    current: 1,
    pageSize: 10,
    total: 100
  }}
/>
```

## SearchFilter

Component tìm kiếm và bộ lọc nâng cao.

### Props

- `searchPlaceholder`: Placeholder cho ô tìm kiếm
- `searchFields`: Cấu hình các trường tìm kiếm nâng cao
- `filters`: Cấu hình bộ lọc
- `onSearch`, `onFilter`, `onReset`: Callback
- `initialValues`: Giá trị khởi tạo

### Ví dụ sử dụng

```jsx
import { SearchFilter } from '../../components/Common';

const searchFields = [
  {
    key: 'name',
    label: 'Tên',
    type: 'text'
  }
];

const filters = [
  {
    key: 'status',
    label: 'Trạng thái',
    type: 'select',
    options: [
      { value: 'active', label: 'Hoạt động' },
      { value: 'inactive', label: 'Không hoạt động' }
    ]
  },
  {
    key: 'dateRange',
    label: 'Khoảng thời gian',
    type: 'dateRange'
  }
];

<SearchFilter
  searchPlaceholder="Tìm kiếm người dùng..."
  searchFields={searchFields}
  filters={filters}
  onSearch={handleSearch}
  onFilter={handleFilter}
  onReset={handleReset}
/>
```

## PageHeader

Component header trang với breadcrumb và actions.

### Props

- `title`: Tiêu đề trang
- `subTitle`: Tiêu đề phụ
- `breadcrumb`: Cấu hình breadcrumb
- `actions`: Các nút thao tác
- `statistics`: Thống kê hiển thị
- `onBack`: Callback nút quay lại

### Ví dụ sử dụng

```jsx
import { PageHeader } from '../../components/Common';

const actions = [
  {
    type: 'primary',
    icon: <PlusOutlined />,
    label: 'Thêm mới',
    onClick: handleCreate
  }
];

const statistics = [
  {
    title: 'Tổng số',
    value: 1234,
    valueStyle: { color: '#1890ff' }
  }
];

<PageHeader
  title="Quản lý người dùng"
  subTitle="Danh sách tất cả người dùng"
  actions={actions}
  statistics={statistics}
/>
```

## Filter Types

### Các loại filter được hỗ trợ:

- `text`: Input text
- `number`: Input number
- `select`: Dropdown select
- `multiSelect`: Multi-select dropdown
- `date`: Date picker
- `dateRange`: Date range picker

### Cấu hình filter:

```jsx
{
  key: 'field_name',        // Tên field
  label: 'Display Label',   // Label hiển thị
  type: 'select',          // Loại filter
  placeholder: 'Chọn...',  // Placeholder
  options: [               // Options cho select
    { value: 'val1', label: 'Label 1' },
    { value: 'val2', label: 'Label 2' }
  ]
}
```

## Best Practices

1. **Sử dụng consistent naming**: Đặt tên props và callbacks theo quy ước
2. **Handle loading states**: Luôn hiển thị trạng thái loading
3. **Error handling**: Xử lý lỗi và hiển thị thông báo phù hợp
4. **Responsive design**: Đảm bảo component hoạt động tốt trên mobile
5. **Accessibility**: Sử dụng đúng ARIA labels và keyboard navigation

## Customization

Các component có thể được tùy chỉnh thông qua:

- CSS classes
- Style props
- Theme configuration
- Custom renderers

Xem thêm tài liệu Ant Design để biết thêm chi tiết về customization.
