/* CreateProduct.module.css */

.createProductContainer {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  margin-bottom: 24px;
}

.backButton {
  margin-bottom: 16px;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title {
  margin: 0 !important;
  color: #1890ff;
}

.actionButtons {
  display: flex;
  gap: 8px;
}

.formContainer {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cardTitle {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
  margin-bottom: 16px;
}

.imageUploadArea {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  background: #fafafa;
  transition: all 0.3s ease;
}

.imageUploadArea:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.variantTable {
  margin-top: 16px;
}

.variantTable .ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
}

.attributeCard {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.attributeCard:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.attributeCardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #f0f0f0;
}

.attributeCardContent {
  padding: 12px 16px;
}

.tagContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.emptyState {
  text-align: center;
  padding: 40px 0;
  color: #999;
  background: #fafafa;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
}

.addButton {
  width: 100%;
  height: 40px;
  border: 1px dashed #d9d9d9;
  background: #fafafa;
  color: #666;
  transition: all 0.3s ease;
}

.addButton:hover {
  border-color: #1890ff;
  color: #1890ff;
  background: #f0f8ff;
}

.inventorySection {
  background: #f6f6f6;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.switchContainer {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.switchLabel {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}

.helpText {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
}

.priceInput {
  width: 100%;
}

.priceInput .ant-input-number {
  width: 100%;
}

.unitConversionCard {
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  margin-bottom: 12px;
  background: #fafafa;
}

.unitConversionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e8e8e8;
}

.unitConversionContent {
  padding: 12px 16px;
}

.formSection {
  margin-bottom: 24px;
}

.sectionTitle {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #1890ff;
}

.cardExtra {
  display: flex;
  gap: 8px;
}

.statusIndicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.statusActive {
  background-color: #52c41a;
}

.statusInactive {
  background-color: #f5222d;
}

.statusPending {
  background-color: #faad14;
}

/* Responsive design */
@media (max-width: 768px) {
  .createProductContainer {
    padding: 16px;
  }

  .headerContent {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .actionButtons {
    justify-content: center;
  }

  .formContainer .ant-col {
    margin-bottom: 16px;
  }
}

/* Animation for modals */
.modalEnter {
  opacity: 0;
  transform: scale(0.9);
}

.modalEnterActive {
  opacity: 1;
  transform: scale(1);
  transition: opacity 300ms, transform 300ms;
}

.modalExit {
  opacity: 1;
  transform: scale(1);
}

.modalExitActive {
  opacity: 0;
  transform: scale(0.9);
  transition: opacity 300ms, transform 300ms;
}

/* Custom scrollbar */
.scrollableContent {
  max-height: 400px;
  overflow-y: auto;
}

.scrollableContent::-webkit-scrollbar {
  width: 6px;
}

.scrollableContent::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.scrollableContent::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.scrollableContent::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
