'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class LoaiSanPham extends Model {
    static associate(models) {
      // Quan hệ với sản phẩm (1-n)
      LoaiSanPham.hasMany(models.SanPham, {
        foreignKey: 'loai_san_pham_id',
        as: 'sanPhamList'
      });
    }
  }

  LoaiSanPham.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    ten: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true,
        len: [2, 100]
      }
    },
    mo_ta: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    nguoi_tao: {
      type: DataTypes.STRING,
      allowNull: true
    },
    nguoi_cap_nhap: {
      type: DataTypes.STRING,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'LoaiSanPham',
    tableName: 'loai_san_pham'
  });

  return LoaiSan<PERSON>ham;
};
