'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.bulkInsert('kho_hang', [
      {
        id: 1,
        ten_kho: '<PERSON>ho chính',
        dia_chi: '123 Đường <PERSON>, Quận 1, TP.HCM',
        mo_ta: '<PERSON>ho hàng chính của công ty',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        id: 2,
        ten_kho: '<PERSON>ho phụ Quận 2',
        dia_chi: '456 Đường <PERSON>n <PERSON>, Quận 2, TP.HCM',
        mo_ta: '<PERSON>ho hàng phụ tại Quận 2',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        id: 3,
        ten_kho: '<PERSON><PERSON>',
        dia_chi: '789 <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>',
        mo_ta: '<PERSON><PERSON> tại <PERSON>à Nội',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        id: 4,
        ten_kho: 'Kho Đà Nẵng',
        dia_chi: '321 Đường Hùng Vương, Hải Châu, Đà Nẵng',
        mo_ta: 'Kho hàng tại Đà Nẵng',
        trang_thai: 1,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        id: 5,
        ten_kho: 'Kho dự phòng',
        dia_chi: '555 Đường Võ Văn Tần, Quận 3, TP.HCM',
        mo_ta: 'Kho dự phòng cho các trường hợp khẩn cấp',
        trang_thai: 0,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('kho_hang', null, {});
  }
};
