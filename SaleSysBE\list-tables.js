const { sequelize } = require('./models');

(async () => {
  try {
    const [results] = await sequelize.query('SHOW TABLES');
    console.log('📋 <PERSON>h sách bảng:');
    results.forEach((row, i) => {
      console.log(`${i+1}. ${Object.values(row)[0]}`);
    });
    console.log(`\nTổng: ${results.length} bảng`);
  } catch (error) {
    console.error('Lỗi:', error.message);
  } finally {
    process.exit(0);
  }
})();
