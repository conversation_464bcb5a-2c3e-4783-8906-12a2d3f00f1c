import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Input,
  Select,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Statistic,
  Tag,
  DatePicker,
  Modal,
  Form,
  InputNumber,
  message,
  Tooltip,
  Divider,
  Upload,
  Checkbox,
  Radio,
  Breadcrumb
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ExportOutlined,
  EyeOutlined,
  PlusOutlined,
  DollarOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined,
  UploadOutlined,
  PaperClipOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

// Hook để lấy dữ liệu công nợ
const useDebtData = (filters) => {
  return useQuery(
    ['debt-management', filters],
    async () => {
      const queryParams = new URLSearchParams();
      
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.dateRange?.[0]) queryParams.append('start_date', filters.dateRange[0]);
      if (filters.dateRange?.[1]) queryParams.append('end_date', filters.dateRange[1]);
      if (filters.page) queryParams.append('page', filters.page);
      if (filters.limit) queryParams.append('limit', filters.limit);

      const response = await fetch(`http://localhost:5000/api/test/debt?${queryParams}`);
      if (!response.ok) {
        throw new Error('Không thể lấy dữ liệu công nợ');
      }
      return response.json();
    },
    {
      keepPreviousData: true,
      refetchOnWindowFocus: false
    }
  );
};

// Hook để tạo phiếu thu/chi
const useCreatePayment = () => {
  const queryClient = useQueryClient();
  
  return useMutation(
    async (paymentData) => {
      console.log('🌐 Calling API with data:', paymentData);

      try {
        console.log('🔗 Making request to: http://localhost:5000/api/test/debt/payment');
        console.log('📦 Request payload:', JSON.stringify(paymentData, null, 2));

        // Test simple endpoint first
        console.log('🧪 Testing simple endpoint first...');
        const testResponse = await fetch('http://localhost:5000/api/test/simple-payment', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ test: 'data' }),
        });
        console.log('🧪 Simple test status:', testResponse.status);

        const response = await fetch(`http://localhost:5000/api/test/debt/payment?t=${Date.now()}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache',
          },
          body: JSON.stringify(paymentData),
        });

        console.log('📡 Response received:');
        console.log('  - Status:', response.status);
        console.log('  - Status Text:', response.statusText);
        console.log('  - Headers:', Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
          console.error(`❌ HTTP Error ${response.status}: ${response.statusText}`);

          // Thử đọc response body để xem lỗi chi tiết
          const responseText = await response.text();
          console.error('❌ Response body:', responseText);

          let errorData = {};
          try {
            errorData = JSON.parse(responseText);
          } catch (e) {
            console.error('❌ Cannot parse error response as JSON');
          }

          throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        console.log('✅ API Success:', result);
        return result;

      } catch (error) {
        console.error('🔥 Network Error:', error);

        // Nếu server không chạy, trả về mock response
        if (error.message.includes('fetch')) {
          console.log('🔄 Server not available, using mock response');
          return {
            success: true,
            message: 'Thu tiền thành công (Mock)',
            data: {
              payment: {
                id: Date.now(),
                ...paymentData,
                created_at: new Date().toISOString()
              }
            }
          };
        }

        throw error;
      }
    },
    {
      onSuccess: () => {
        message.success('Tạo phiếu thu/chi thành công!');
        queryClient.invalidateQueries(['debt-management']);
      },
      onError: (error) => {
        message.error(`Lỗi: ${error.message}`);
      }
    }
  );
};

const DebtManagement = () => {
  const navigate = useNavigate();
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    customerGroup: '',
    debtRange: '',
    overdueOnly: false,
    dateRange: null,
    page: 1,
    limit: 20
  });

  const [savedFilters, setSavedFilters] = useState([
    { name: 'Khách nợ quá hạn > 60 ngày', filters: { overdueOnly: true, debtRange: 'high' } },
    { name: 'VIP có công nợ', filters: { customerGroup: 'vip', status: 'warning' } },
    { name: 'Đại lý nợ cao', filters: { customerGroup: 'agency', debtRange: 'high' } }
  ]);

  const [advancedFilterVisible, setAdvancedFilterVisible] = useState(false);
  
  const [paymentModal, setPaymentModal] = useState({
    visible: false,
    customer: null,
    type: 'thu' // 'thu' hoặc 'chi'
  });

  const [form] = Form.useForm();
  const { data, isLoading, error } = useDebtData(filters);
  const createPaymentMutation = useCreatePayment();

  // Cấu hình cột cho bảng công nợ
  const columns = [
    {
      title: 'STT',
      key: 'index',
      width: 60,
      render: (_, __, index) => (filters.page - 1) * filters.limit + index + 1
    },
    {
      title: 'Khách hàng',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 200,
      render: (text, record) => (
        <Space direction="vertical" size="small">
          <Button
            type="link"
            onClick={() => handleViewDetail(record)}
            style={{ padding: 0, height: 'auto', fontWeight: 'bold' }}
          >
            {text}
          </Button>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            📞 {record.customer_phone}
          </Text>
        </Space>
      )
    },
    {
      title: 'Tổng nợ',
      dataIndex: 'total_debt',
      key: 'total_debt',
      width: 150,
      align: 'right',
      render: (value) => (
        <Text strong style={{ color: value > 0 ? '#ff4d4f' : '#52c41a' }}>
          {new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
          }).format(value)}
        </Text>
      )
    },
    {
      title: 'Nợ quá hạn',
      dataIndex: 'overdue_debt',
      key: 'overdue_debt',
      width: 150,
      align: 'right',
      render: (value) => (
        <Text style={{ color: value > 0 ? '#ff4d4f' : '#666' }}>
          {new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
          }).format(value)}
        </Text>
      )
    },
    {
      title: 'Số đơn hàng',
      dataIndex: 'order_count',
      key: 'order_count',
      width: 100,
      align: 'center'
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'last_updated',
      key: 'last_updated',
      width: 120,
      render: (date) => dayjs(date).format('DD/MM/YYYY')
    },
    {
      title: 'Nhóm KH',
      dataIndex: 'customer_group',
      key: 'customer_group',
      width: 100,
      render: (group) => {
        const groupConfig = {
          'vip': { color: 'gold', text: 'VIP' },
          'agency': { color: 'blue', text: 'Đại lý' },
          'retail': { color: 'default', text: 'Lẻ' },
          'wholesale': { color: 'purple', text: 'Sỉ' }
        };
        const config = groupConfig[group] || groupConfig.retail;
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status, record) => {
        let statusConfig;
        if (record.total_debt <= 0) {
          statusConfig = { color: 'success', text: 'Đã thu đủ', icon: '✓' };
        } else if (record.overdue_debt > 0) {
          statusConfig = { color: 'error', text: 'Quá hạn', icon: '⚠' };
        } else if (record.total_debt > 5000000) {
          statusConfig = { color: 'warning', text: 'Cảnh báo', icon: '!' };
        } else {
          statusConfig = { color: 'processing', text: 'Còn nợ', icon: '○' };
        }

        return (
          <Tag color={statusConfig.color} style={{ fontWeight: 'bold' }}>
            {statusConfig.icon} {statusConfig.text}
          </Tag>
        );
      }
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="Xem chi tiết công nợ">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
              style={{ color: '#1890ff' }}
            />
          </Tooltip>
          <Tooltip title="Tạo phiếu thu tiền">
            <Button
              type="text"
              icon={<DollarOutlined />}
              onClick={() => handleCreatePayment(record, 'thu')}
              disabled={record.total_debt <= 0}
              style={{ color: record.total_debt > 0 ? '#52c41a' : '#d9d9d9' }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // Xử lý xem chi tiết
  const handleViewDetail = (customer) => {
    console.log('View detail for customer:', customer);
    if (!customer.customer_id) {
      message.error('Không tìm thấy ID khách hàng');
      return;
    }

    message.loading('Đang chuyển đến trang chi tiết...', 0.5);
    navigate(`/accounting/debt/${customer.customer_id}`);
  };

  // Xử lý tạo phiếu thu/chi
  const handleCreatePayment = (customer, type) => {
    setPaymentModal({
      visible: true,
      customer,
      type
    });
    form.resetFields();
    form.setFieldsValue({
      customer_id: customer.customer_id,
      customer_name: customer.customer_name,
      type,
      amount: type === 'thu' ? customer.total_debt : 0,
      payment_method: 'cash', // Default value
      is_partial_payment: false
    });
  };

  // Xử lý submit phiếu thu/chi
  const handlePaymentSubmit = async (values) => {
    try {
      console.log('🚀 Submitting payment:', values);
      console.log('🎯 Customer:', paymentModal.customer);

      const paymentData = {
        ...values,
        customer_id: paymentModal.customer.customer_id,
        type: paymentModal.type
      };

      console.log('📤 Sending payment data:', paymentData);

      const result = await createPaymentMutation.mutateAsync(paymentData);

      console.log('✅ Payment result:', result);

      // Hiển thị thông báo thành công với thông tin chi tiết
      const amountFormatted = new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(paymentData.amount);

      message.success(
        `Thu tiền thành công! Đã thu ${amountFormatted} từ ${paymentModal.customer.customer_name}. Công nợ đã được cập nhật.`,
        5 // Hiển thị 5 giây
      );

      setPaymentModal({ visible: false, customer: null, type: 'thu' });
      form.resetFields();

      // Refresh data để cập nhật bảng
      window.location.reload(); // Tạm thời reload để thấy thay đổi

    } catch (error) {
      console.error('❌ Error creating payment:', error);
      message.error(error.message || 'Có lỗi xảy ra khi tạo phiếu thu/chi');
    }
  };

  // Xử lý thay đổi bộ lọc
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset về trang đầu khi filter
    }));
  };

  // Xử lý thay đổi trang
  const handleTableChange = (pagination) => {
    setFilters(prev => ({
      ...prev,
      page: pagination.current,
      limit: pagination.pageSize
    }));
  };

  if (error) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Text type="danger">Lỗi: {error.message}</Text>
        </div>
      </Card>
    );
  }

  const debtData = data?.data || [];
  const stats = data?.stats || {};
  const pagination = data?.pagination || {};

  return (
    <div style={{ padding: '24px' }}>
      {/* Breadcrumb */}
      <Breadcrumb style={{ marginBottom: '16px' }}>
        <Breadcrumb.Item>Kế toán</Breadcrumb.Item>
        <Breadcrumb.Item>Quản lý công nợ</Breadcrumb.Item>
      </Breadcrumb>

      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <UserOutlined style={{ marginRight: '8px' }} />
          Quản lý công nợ khách hàng
        </Title>
        <Text type="secondary">
          Theo dõi và quản lý công nợ của khách hàng. Click vào tên khách hàng hoặc biểu tượng mắt để xem chi tiết.
        </Text>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng công nợ"
              value={stats.total_debt || 0}
              formatter={(value) => new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              }).format(value)}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Nợ quá hạn"
              value={stats.overdue_debt || 0}
              formatter={(value) => new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              }).format(value)}
              valueStyle={{ color: '#ff7875' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Số khách hàng có nợ"
              value={stats.customers_with_debt || 0}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Khách hàng quá hạn"
              value={stats.overdue_customers || 0}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]} align="middle">
          <Col span={6}>
            <Input
              placeholder="Tìm kiếm khách hàng..."
              prefix={<SearchOutlined />}
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Trạng thái"
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="normal">Bình thường</Option>
              <Option value="warning">Cảnh báo</Option>
              <Option value="overdue">Quá hạn</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              placeholder={['Từ ngày', 'Đến ngày']}
              value={filters.dateRange}
              onChange={(dates) => handleFilterChange('dateRange', dates)}
              format="DD/MM/YYYY"
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={8}>
            <Space>
              <Button
                icon={<FilterOutlined />}
                onClick={() => setAdvancedFilterVisible(!advancedFilterVisible)}
              >
                Bộ lọc nâng cao
              </Button>
              <Button icon={<ExportOutlined />}>
                Xuất Excel
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Advanced Filters */}
      {advancedFilterVisible && (
        <Card title="Bộ lọc nâng cao" style={{ marginBottom: '24px' }}>
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text strong>Nhóm khách hàng</Text>
                <Select
                  placeholder="Chọn nhóm"
                  value={filters.customerGroup}
                  onChange={(value) => handleFilterChange('customerGroup', value)}
                  allowClear
                  style={{ width: '100%' }}
                >
                  <Option value="vip">VIP</Option>
                  <Option value="agency">Đại lý</Option>
                  <Option value="wholesale">Khách sỉ</Option>
                  <Option value="retail">Khách lẻ</Option>
                </Select>
              </Space>
            </Col>
            <Col span={6}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text strong>Mức công nợ</Text>
                <Select
                  placeholder="Chọn mức nợ"
                  value={filters.debtRange}
                  onChange={(value) => handleFilterChange('debtRange', value)}
                  allowClear
                  style={{ width: '100%' }}
                >
                  <Option value="low">Dưới 1 triệu</Option>
                  <Option value="medium">1-5 triệu</Option>
                  <Option value="high">Trên 5 triệu</Option>
                  <Option value="very_high">Trên 10 triệu</Option>
                </Select>
              </Space>
            </Col>
            <Col span={6}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text strong>Bộ lọc nhanh</Text>
                <Select
                  placeholder="Chọn bộ lọc đã lưu"
                  onChange={(value) => {
                    const savedFilter = savedFilters.find(f => f.name === value);
                    if (savedFilter) {
                      setFilters(prev => ({ ...prev, ...savedFilter.filters }));
                    }
                  }}
                  style={{ width: '100%' }}
                >
                  {savedFilters.map(filter => (
                    <Option key={filter.name} value={filter.name}>
                      {filter.name}
                    </Option>
                  ))}
                </Select>
              </Space>
            </Col>
            <Col span={6}>
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <Text strong>Tùy chọn khác</Text>
                <Space direction="vertical">
                  <label>
                    <input
                      type="checkbox"
                      checked={filters.overdueOnly}
                      onChange={(e) => handleFilterChange('overdueOnly', e.target.checked)}
                      style={{ marginRight: '8px' }}
                    />
                    Chỉ hiển thị nợ quá hạn
                  </label>
                </Space>
              </Space>
            </Col>
          </Row>
        </Card>
      )}

      {/* Main Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={debtData}
          rowKey="customer_id"
          loading={isLoading}
          pagination={{
            current: pagination.page || 1,
            pageSize: pagination.limit || 20,
            total: pagination.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} của ${total} khách hàng`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Payment Modal */}
      <Modal
        title={`Tạo phiếu ${paymentModal.type === 'thu' ? 'thu tiền' : 'chi tiền'}`}
        open={paymentModal.visible}
        onCancel={() => setPaymentModal({ visible: false, customer: null, type: 'thu' })}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handlePaymentSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Khách hàng"
                name="customer_name"
              >
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Loại phiếu"
                name="type"
              >
                <Select disabled>
                  <Option value="thu">Phiếu thu</Option>
                  <Option value="chi">Phiếu chi</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label={`Tổng nợ hiện tại: ${new Intl.NumberFormat('vi-VN', {
                  style: 'currency',
                  currency: 'VND'
                }).format(paymentModal.customer?.total_debt || 0)}`}
                name="amount"
                rules={[
                  { required: true, message: 'Vui lòng nhập số tiền!' },
                  { type: 'number', min: 1, message: 'Số tiền phải lớn hơn 0!' },
                  {
                    validator: (_, value) => {
                      if (value && value > (paymentModal.customer?.total_debt || 0)) {
                        return Promise.reject(new Error('Số tiền không được vượt quá tổng nợ!'));
                      }
                      return Promise.resolve();
                    }
                  }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value.replace(/\$\s?|(,*)/g, '')}
                  placeholder="Nhập số tiền thu"
                  max={paymentModal.customer?.total_debt}
                  min={1}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Phương thức thanh toán (chỉ để ghi nhận)"
                name="payment_method"
                initialValue="cash"
              >
                <Select placeholder="Chọn phương thức thanh toán">
                  <Option value="cash">💵 Tiền mặt</Option>
                  <Option value="transfer">🏦 Chuyển khoản</Option>
                  <Option value="card">💳 Thẻ tín dụng/ghi nợ</Option>
                  <Option value="check">📝 Séc</Option>
                  <Option value="other">🔄 Khác</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="Đính kèm chứng từ"
            name="attachments"
          >
            <Upload
              multiple
              beforeUpload={() => false}
              listType="text"
              maxCount={5}
            >
              <Button icon={<UploadOutlined />}>
                Tải lên hóa đơn/ảnh chuyển khoản
              </Button>
            </Upload>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Hỗ trợ: JPG, PNG, PDF. Tối đa 5 file.
            </Text>
          </Form.Item>

          <Form.Item
            label="Ghi chú"
            name="note"
          >
            <TextArea rows={3} placeholder="Ghi chú về phiếu thu/chi..." />
          </Form.Item>

          <Form.Item name="is_partial_payment" valuePropName="checked">
            <Checkbox>
              Đây là thanh toán từng phần (không đóng hoàn toàn công nợ)
            </Checkbox>
          </Form.Item>

          <Divider />

          <div style={{
            backgroundColor: '#f6ffed',
            border: '1px solid #b7eb8f',
            borderRadius: '6px',
            padding: '12px',
            marginBottom: '16px'
          }}>
            <Text strong style={{ color: '#52c41a' }}>💡 Lưu ý:</Text>
            <br />
            <Text type="secondary">
              • Phương thức thanh toán chỉ để ghi nhận, không ảnh hưởng đến xử lý
              <br />
              • Số tiền thu sẽ được <strong>trừ trực tiếp vào công nợ</strong> của khách hàng
              <br />
              • Công nợ còn lại: {new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              }).format(Math.max(0, (paymentModal.customer?.total_debt || 0) - (form.getFieldValue('amount') || 0)))}
            </Text>
          </div>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setPaymentModal({ visible: false, customer: null, type: 'thu' })}>
                Hủy
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createPaymentMutation.isLoading}
                icon={<DollarOutlined />}
              >
                Thu tiền và cập nhật công nợ
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DebtManagement;
