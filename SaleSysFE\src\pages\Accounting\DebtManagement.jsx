import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Input, 
  Select, 
  Button, 
  Space, 
  Typography, 
  Row,
  Col,
  Statistic,
  Tag,
  DatePicker,
  Modal,
  Form,
  InputNumber,
  message,
  Tooltip,
  Divider
} from 'antd';
import { 
  SearchOutlined, 
  FilterOutlined, 
  ExportOutlined,
  EyeOutlined,
  PlusOutlined,
  DollarOutlined,
  UserOutlined,
  CalendarOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

// Hook để lấy dữ liệu công nợ
const useDebtData = (filters) => {
  return useQuery(
    ['debt-management', filters],
    async () => {
      const queryParams = new URLSearchParams();
      
      if (filters.search) queryParams.append('search', filters.search);
      if (filters.status) queryParams.append('status', filters.status);
      if (filters.dateRange?.[0]) queryParams.append('start_date', filters.dateRange[0]);
      if (filters.dateRange?.[1]) queryParams.append('end_date', filters.dateRange[1]);
      if (filters.page) queryParams.append('page', filters.page);
      if (filters.limit) queryParams.append('limit', filters.limit);

      const response = await fetch(`http://localhost:5002/api/test/debt?${queryParams}`);
      if (!response.ok) {
        throw new Error('Không thể lấy dữ liệu công nợ');
      }
      return response.json();
    },
    {
      keepPreviousData: true,
      refetchOnWindowFocus: false
    }
  );
};

// Hook để tạo phiếu thu/chi
const useCreatePayment = () => {
  const queryClient = useQueryClient();
  
  return useMutation(
    async (paymentData) => {
      const response = await fetch('http://localhost:5002/api/test/debt/payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentData),
      });
      
      if (!response.ok) {
        throw new Error('Không thể tạo phiếu thu/chi');
      }
      
      return response.json();
    },
    {
      onSuccess: () => {
        message.success('Tạo phiếu thu/chi thành công!');
        queryClient.invalidateQueries(['debt-management']);
      },
      onError: (error) => {
        message.error(`Lỗi: ${error.message}`);
      }
    }
  );
};

const DebtManagement = () => {
  const [filters, setFilters] = useState({
    search: '',
    status: '',
    dateRange: null,
    page: 1,
    limit: 20
  });
  
  const [paymentModal, setPaymentModal] = useState({
    visible: false,
    customer: null,
    type: 'thu' // 'thu' hoặc 'chi'
  });

  const [form] = Form.useForm();
  const { data, isLoading, error } = useDebtData(filters);
  const createPaymentMutation = useCreatePayment();

  // Cấu hình cột cho bảng công nợ
  const columns = [
    {
      title: 'STT',
      key: 'index',
      width: 60,
      render: (_, __, index) => (filters.page - 1) * filters.limit + index + 1
    },
    {
      title: 'Khách hàng',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 200,
      render: (text, record) => (
        <Space direction="vertical" size="small">
          <Text strong>{text}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.customer_phone}
          </Text>
        </Space>
      )
    },
    {
      title: 'Tổng nợ',
      dataIndex: 'total_debt',
      key: 'total_debt',
      width: 150,
      align: 'right',
      render: (value) => (
        <Text strong style={{ color: value > 0 ? '#ff4d4f' : '#52c41a' }}>
          {new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
          }).format(value)}
        </Text>
      )
    },
    {
      title: 'Nợ quá hạn',
      dataIndex: 'overdue_debt',
      key: 'overdue_debt',
      width: 150,
      align: 'right',
      render: (value) => (
        <Text style={{ color: value > 0 ? '#ff4d4f' : '#666' }}>
          {new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
          }).format(value)}
        </Text>
      )
    },
    {
      title: 'Số đơn hàng',
      dataIndex: 'order_count',
      key: 'order_count',
      width: 100,
      align: 'center'
    },
    {
      title: 'Ngày cập nhật',
      dataIndex: 'last_updated',
      key: 'last_updated',
      width: 120,
      render: (date) => dayjs(date).format('DD/MM/YYYY')
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => {
        const statusConfig = {
          'normal': { color: 'green', text: 'Bình thường' },
          'warning': { color: 'orange', text: 'Cảnh báo' },
          'overdue': { color: 'red', text: 'Quá hạn' }
        };
        const config = statusConfig[status] || statusConfig.normal;
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space>
          <Tooltip title="Xem chi tiết">
            <Button 
              type="text" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="Thu tiền">
            <Button 
              type="text" 
              icon={<DollarOutlined />}
              onClick={() => handleCreatePayment(record, 'thu')}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // Xử lý xem chi tiết
  const handleViewDetail = (customer) => {
    // TODO: Navigate to detail page
    console.log('View detail for customer:', customer);
  };

  // Xử lý tạo phiếu thu/chi
  const handleCreatePayment = (customer, type) => {
    setPaymentModal({
      visible: true,
      customer,
      type
    });
    form.resetFields();
    form.setFieldsValue({
      customer_id: customer.customer_id,
      customer_name: customer.customer_name,
      type,
      amount: type === 'thu' ? customer.total_debt : 0
    });
  };

  // Xử lý submit phiếu thu/chi
  const handlePaymentSubmit = async (values) => {
    try {
      await createPaymentMutation.mutateAsync({
        ...values,
        customer_id: paymentModal.customer.customer_id
      });
      setPaymentModal({ visible: false, customer: null, type: 'thu' });
      form.resetFields();
    } catch (error) {
      console.error('Error creating payment:', error);
    }
  };

  // Xử lý thay đổi bộ lọc
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset về trang đầu khi filter
    }));
  };

  // Xử lý thay đổi trang
  const handleTableChange = (pagination) => {
    setFilters(prev => ({
      ...prev,
      page: pagination.current,
      limit: pagination.pageSize
    }));
  };

  if (error) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Text type="danger">Lỗi: {error.message}</Text>
        </div>
      </Card>
    );
  }

  const debtData = data?.data || [];
  const stats = data?.stats || {};
  const pagination = data?.pagination || {};

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <UserOutlined style={{ marginRight: '8px' }} />
          Quản lý công nợ khách hàng
        </Title>
      </div>

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng công nợ"
              value={stats.total_debt || 0}
              formatter={(value) => new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              }).format(value)}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Nợ quá hạn"
              value={stats.overdue_debt || 0}
              formatter={(value) => new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
              }).format(value)}
              valueStyle={{ color: '#ff7875' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Số khách hàng có nợ"
              value={stats.customers_with_debt || 0}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Khách hàng quá hạn"
              value={stats.overdue_customers || 0}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]} align="middle">
          <Col span={6}>
            <Input
              placeholder="Tìm kiếm khách hàng..."
              prefix={<SearchOutlined />}
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Trạng thái"
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="normal">Bình thường</Option>
              <Option value="warning">Cảnh báo</Option>
              <Option value="overdue">Quá hạn</Option>
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              placeholder={['Từ ngày', 'Đến ngày']}
              value={filters.dateRange}
              onChange={(dates) => handleFilterChange('dateRange', dates)}
              format="DD/MM/YYYY"
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={8}>
            <Space>
              <Button icon={<FilterOutlined />}>
                Bộ lọc nâng cao
              </Button>
              <Button icon={<ExportOutlined />}>
                Xuất Excel
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Main Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={debtData}
          rowKey="customer_id"
          loading={isLoading}
          pagination={{
            current: pagination.page || 1,
            pageSize: pagination.limit || 20,
            total: pagination.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} của ${total} khách hàng`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* Payment Modal */}
      <Modal
        title={`Tạo phiếu ${paymentModal.type === 'thu' ? 'thu tiền' : 'chi tiền'}`}
        open={paymentModal.visible}
        onCancel={() => setPaymentModal({ visible: false, customer: null, type: 'thu' })}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handlePaymentSubmit}
        >
          <Form.Item
            label="Khách hàng"
            name="customer_name"
          >
            <Input disabled />
          </Form.Item>

          <Form.Item
            label="Loại phiếu"
            name="type"
          >
            <Select disabled>
              <Option value="thu">Phiếu thu</Option>
              <Option value="chi">Phiếu chi</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="Số tiền"
            name="amount"
            rules={[
              { required: true, message: 'Vui lòng nhập số tiền!' },
              { type: 'number', min: 1, message: 'Số tiền phải lớn hơn 0!' }
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/\$\s?|(,*)/g, '')}
              placeholder="Nhập số tiền"
            />
          </Form.Item>

          <Form.Item
            label="Ghi chú"
            name="note"
          >
            <TextArea rows={3} placeholder="Ghi chú về phiếu thu/chi..." />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setPaymentModal({ visible: false, customer: null, type: 'thu' })}>
                Hủy
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={createPaymentMutation.isLoading}
              >
                Tạo phiếu
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DebtManagement;
