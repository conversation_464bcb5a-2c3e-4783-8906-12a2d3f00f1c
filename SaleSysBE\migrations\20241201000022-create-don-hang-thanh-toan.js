'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('don_hang_thanh_toan', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      don_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'don_hang',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      phuong_thuc: {
        type: Sequelize.ENUM('Tien_mat', 'Chuyen_khoan', 'VNPay', 'MoMo', 'ZaloPay', 'The_tin_dung'),
        allowNull: false,
        comment: 'Tien_mat, Chuyen_khoan, V<PERSON>ay, ...'
      },
      so_tien: {
        type: Sequelize.DOUBLE,
        allowNull: false,
        validate: {
          min: 0
        }
      },
      ngay_thanh_toan: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      trang_thai: {
        type: Sequelize.ENUM('da_thanh_toan', 'cho_thanh_toan', 'that_bai'),
        defaultValue: 'da_thanh_toan'
      },
      ghi_chu: {
        type: Sequelize.TEXT,
        allowNull: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('don_hang_thanh_toan', ['don_hang_id']);
    await queryInterface.addIndex('don_hang_thanh_toan', ['phuong_thuc']);
    await queryInterface.addIndex('don_hang_thanh_toan', ['trang_thai']);
    await queryInterface.addIndex('don_hang_thanh_toan', ['ngay_thanh_toan']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('don_hang_thanh_toan');
  }
};
