const { uploadImage, uploadMultipleImages, deleteImage } = require('../utils/cloudinaryUpload');
const { simpleUploadImage } = require('../utils/simpleCloudinaryUpload');

/**
 * Upload single image
 */
const uploadSingleImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Không có file nào được upload'
      });
    }

    const { folder = 'general', width, height } = req.body;

    console.log('🎯 Upload request:', {
      folder,
      width,
      height,
      fileName: req.file.originalname,
      fileSize: req.file.size,
      mimeType: req.file.mimetype
    });

    // Use simple upload to avoid transformation errors
    const result = await simpleUploadImage(req.file.buffer, {
      folder,
      width: width ? parseInt(width) : undefined,
      height: height ? parseInt(height) : undefined,
      public_id: `${folder}_${Date.now()}`
    });

    if (result.success) {
      res.status(200).json({
        success: true,
        message: 'Upload ảnh thành công',
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Lỗi upload ảnh',
        error: result.error
      });
    }

  } catch (error) {
    console.error('Lỗi upload single image:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: error.message
    });
  }
};

/**
 * Upload multiple images
 */
const uploadMultipleImagesController = async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Không có file nào được upload'
      });
    }

    const { folder = 'general', width, height } = req.body;

    const fileBuffers = req.files.map(file => file.buffer);

    const result = await uploadMultipleImages(fileBuffers, {
      folder,
      width: width ? parseInt(width) : undefined,
      height: height ? parseInt(height) : undefined,
      public_id: `${folder}_${Date.now()}`
    });

    if (result.success) {
      res.status(200).json({
        success: true,
        message: `Upload thành công ${result.uploaded} ảnh`,
        data: result.data,
        uploaded: result.uploaded,
        failed: result.failed
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Lỗi upload ảnh',
        error: result.error,
        errors: result.errors
      });
    }

  } catch (error) {
    console.error('Lỗi upload multiple images:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: error.message
    });
  }
};

/**
 * Delete image
 */
const deleteImageController = async (req, res) => {
  try {
    const { publicId } = req.params;

    if (!publicId) {
      return res.status(400).json({
        success: false,
        message: 'Public ID không được để trống'
      });
    }

    const result = await deleteImage(publicId);

    if (result.success) {
      res.status(200).json({
        success: true,
        message: 'Xóa ảnh thành công',
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Lỗi xóa ảnh',
        error: result.error
      });
    }

  } catch (error) {
    console.error('Lỗi delete image:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: error.message
    });
  }
};

/**
 * Upload ảnh cho sản phẩm
 */
const uploadProductImage = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'Không có file nào được upload'
      });
    }

    const { productId } = req.body;

    const result = await uploadImage(req.file.buffer, {
      folder: 'products',
      width: 800,
      height: 600,
      public_id: `product_${productId}_${Date.now()}`
    });

    if (result.success) {
      // Có thể lưu vào database ở đây
      // await AnhSanPham.create({
      //   url: result.data.url,
      //   san_pham_id: productId,
      //   public_id: result.data.public_id
      // });

      res.status(200).json({
        success: true,
        message: 'Upload ảnh sản phẩm thành công',
        data: result.data
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Lỗi upload ảnh sản phẩm',
        error: result.error
      });
    }

  } catch (error) {
    console.error('Lỗi upload product image:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi server',
      error: error.message
    });
  }
};

module.exports = {
  uploadSingleImage,
  uploadMultipleImagesController,
  deleteImageController,
  uploadProductImage
};
