import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Radio,
  Button,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  Card,
  message
} from 'antd';
import {
  DollarOutlined,
  CreditCardOutlined,
  BankOutlined,
  WalletOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const PaymentModal = ({
  visible,
  onCancel,
  onSubmit,
  orderData,
  loading = false
}) => {
  const [form] = Form.useForm();
  const [paymentType, setPaymentType] = useState('full'); // 'full' hoặc 'partial'
  const [paymentAmount, setPaymentAmount] = useState(0);

  // Reset form khi modal mở/đóng
  useEffect(() => {
    if (visible && orderData) {
      const remainingAmount = (orderData.tong_phai_tra || 0) - (orderData.tong_da_tra || 0);
      setPaymentAmount(remainingAmount);
      form.setFieldsValue({
        payment_method: 'cash',
        amount: remainingAmount,
        note: `Thanh toán đơn hàng ${orderData.ma_don_hang}`
      });
    } else {
      form.resetFields();
      setPaymentType('full');
      setPaymentAmount(0);
    }
  }, [visible, orderData, form]);

  // Tính toán số tiền
  const totalAmount = orderData?.tong_phai_tra || 0;
  const paidAmount = orderData?.tong_da_tra || 0;
  const remainingAmount = totalAmount - paidAmount;

  // Xử lý thay đổi loại thanh toán
  const handlePaymentTypeChange = (e) => {
    const type = e.target.value;
    setPaymentType(type);
    
    if (type === 'full') {
      setPaymentAmount(remainingAmount);
      form.setFieldValue('amount', remainingAmount);
    } else {
      setPaymentAmount(0);
      form.setFieldValue('amount', 0);
    }
  };

  // Xử lý submit
  const handleSubmit = async (values) => {
    try {
      if (values.amount <= 0) {
        message.error('Số tiền thanh toán phải lớn hơn 0');
        return;
      }

      if (values.amount > remainingAmount) {
        message.error('Số tiền thanh toán không được vượt quá số tiền còn nợ');
        return;
      }

      const paymentData = {
        customer_id: orderData.khach_hang_id,
        type: 'thu',
        amount: values.amount,
        payment_method: values.payment_method,
        note: values.note || `Thanh toán đơn hàng ${orderData.ma_don_hang}`,
        order_id: orderData.id,
        is_partial_payment: paymentType === 'partial'
      };

      await onSubmit(paymentData);
      form.resetFields();
    } catch (error) {
      console.error('Error submitting payment:', error);
    }
  };

  // Payment method options
  const paymentMethods = [
    { value: 'cash', label: 'Tiền mặt', icon: <DollarOutlined /> },
    { value: 'transfer', label: 'Chuyển khoản', icon: <BankOutlined /> },
    { value: 'card', label: 'Thẻ tín dụng', icon: <CreditCardOutlined /> },
    { value: 'other', label: 'Khác', icon: <WalletOutlined /> }
  ];

  return (
    <Modal
      title={
        <Space>
          <DollarOutlined />
          <span>Thanh toán đơn hàng</span>
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      {orderData && (
        <>
          {/* Thông tin đơn hàng */}
          <Card size="small" style={{ marginBottom: 16, backgroundColor: '#f8f9fa' }}>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>Mã đơn hàng: </Text>
                <Text>{orderData.ma_don_hang}</Text>
              </Col>
              <Col span={12}>
                <Text strong>Khách hàng: </Text>
                <Text>{orderData.ten_khach_hang || 'Khách lẻ'}</Text>
              </Col>
            </Row>
            <Divider style={{ margin: '8px 0' }} />
            <Row gutter={16}>
              <Col span={8}>
                <Text type="secondary">Tổng tiền:</Text>
                <br />
                <Text strong style={{ color: '#1890ff', fontSize: 16 }}>
                  {totalAmount.toLocaleString('vi-VN')}đ
                </Text>
              </Col>
              <Col span={8}>
                <Text type="secondary">Đã thanh toán:</Text>
                <br />
                <Text strong style={{ color: '#52c41a', fontSize: 16 }}>
                  {paidAmount.toLocaleString('vi-VN')}đ
                </Text>
              </Col>
              <Col span={8}>
                <Text type="secondary">Còn nợ:</Text>
                <br />
                <Text strong style={{ color: '#f5222d', fontSize: 16 }}>
                  {remainingAmount.toLocaleString('vi-VN')}đ
                </Text>
              </Col>
            </Row>
          </Card>

          {/* Form thanh toán */}
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
            {/* Loại thanh toán */}
            <Form.Item label="Tùy chọn thanh toán">
              <Radio.Group
                value={paymentType}
                onChange={handlePaymentTypeChange}
                style={{ width: '100%' }}
              >
                <div style={{ display: 'flex', gap: '8px' }}>
                  <Radio.Button
                    value="full"
                    style={{
                      flex: 1,
                      textAlign: 'center',
                      height: '60px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexDirection: 'column',
                      fontSize: '14px',
                      fontWeight: paymentType === 'full' ? 'bold' : 'normal'
                    }}
                  >
                    <div>💰 Thanh toán toàn bộ</div>
                    <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                      {remainingAmount.toLocaleString('vi-VN')}đ
                    </div>
                  </Radio.Button>
                  <Radio.Button
                    value="partial"
                    style={{
                      flex: 1,
                      textAlign: 'center',
                      height: '60px',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      flexDirection: 'column',
                      fontSize: '14px',
                      fontWeight: paymentType === 'partial' ? 'bold' : 'normal'
                    }}
                  >
                    <div>💳 Thanh toán một phần</div>
                    <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                      Nhập số tiền tùy chọn
                    </div>
                  </Radio.Button>
                </div>
              </Radio.Group>
            </Form.Item>

            {/* Số tiền thanh toán */}
            <Form.Item
              label={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                  <span>Số tiền thanh toán</span>
                  <span style={{ fontSize: '12px', color: '#666' }}>
                    {paymentType === 'full' ? 'Tự động điền' : `Tối đa: ${remainingAmount.toLocaleString('vi-VN')}đ`}
                  </span>
                </div>
              }
              name="amount"
              rules={[
                { required: true, message: 'Vui lòng nhập số tiền thanh toán' },
                {
                  validator: (_, value) => {
                    if (value <= 0) {
                      return Promise.reject('Số tiền phải lớn hơn 0');
                    }
                    if (value > remainingAmount) {
                      return Promise.reject('Số tiền không được vượt quá số tiền còn nợ');
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <InputNumber
                style={{ width: '100%', fontSize: '16px' }}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
                placeholder={paymentType === 'full' ? 'Sẽ tự động điền số tiền còn nợ' : 'Nhập số tiền thanh toán'}
                min={0}
                max={remainingAmount}
                disabled={paymentType === 'full'}
                addonAfter="VND"
                size="large"
              />
            </Form.Item>

            {/* Phương thức thanh toán */}
            <Form.Item
              label="Phương thức thanh toán"
              name="payment_method"
              rules={[{ required: true, message: 'Vui lòng chọn phương thức thanh toán' }]}
            >
              <Select placeholder="Chọn phương thức thanh toán">
                {paymentMethods.map(method => (
                  <Option key={method.value} value={method.value}>
                    <Space>
                      {method.icon}
                      {method.label}
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            {/* Ghi chú */}
            <Form.Item
              label="Ghi chú"
              name="note"
            >
              <TextArea
                rows={3}
                placeholder="Nhập ghi chú cho thanh toán (tùy chọn)"
              />
            </Form.Item>

            {/* Buttons */}
            <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
              <Space>
                <Button onClick={onCancel} size="large">
                  Hủy
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  icon={<DollarOutlined />}
                  size="large"
                  style={{
                    backgroundColor: '#52c41a',
                    borderColor: '#52c41a',
                    fontWeight: 'bold'
                  }}
                >
                  {paymentType === 'full' ? 'Thanh toán toàn bộ' : 'Thanh toán một phần'}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </>
      )}
    </Modal>
  );
};

export default PaymentModal;
