import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Radio,
  Button,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  Card,
  message
} from 'antd';
import {
  DollarOutlined,
  CreditCardOutlined,
  BankOutlined,
  WalletOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const PaymentModal = ({
  visible,
  onCancel,
  onSubmit,
  orderData,
  loading = false
}) => {
  const [form] = Form.useForm();
  const [paymentType, setPaymentType] = useState('full'); // 'full' hoặc 'partial'
  const [paymentAmount, setPaymentAmount] = useState(0);

  // Reset form khi modal mở/đóng
  useEffect(() => {
    if (visible && orderData) {
      const remainingAmount = (orderData.tong_phai_tra || 0) - (orderData.tong_da_tra || 0);
      setPaymentAmount(remainingAmount);
      form.setFieldsValue({
        payment_method: 'cash',
        amount: remainingAmount,
        note: `Thanh toán đơn hàng ${orderData.ma_don_hang}`
      });
    } else {
      form.resetFields();
      setPaymentType('full');
      setPaymentAmount(0);
    }
  }, [visible, orderData, form]);

  // Tính toán số tiền
  const totalAmount = orderData?.tong_phai_tra || 0;
  const paidAmount = orderData?.tong_da_tra || 0;
  const remainingAmount = totalAmount - paidAmount;

  // Xử lý thay đổi loại thanh toán
  const handlePaymentTypeChange = (e) => {
    const type = e.target.value;
    setPaymentType(type);
    
    if (type === 'full') {
      setPaymentAmount(remainingAmount);
      form.setFieldValue('amount', remainingAmount);
    } else {
      setPaymentAmount(0);
      form.setFieldValue('amount', 0);
    }
  };

  // Xử lý submit
  const handleSubmit = async (values) => {
    try {
      if (values.amount <= 0) {
        message.error('Số tiền thanh toán phải lớn hơn 0');
        return;
      }

      if (values.amount > remainingAmount) {
        message.error('Số tiền thanh toán không được vượt quá số tiền còn nợ');
        return;
      }

      const paymentData = {
        customer_id: orderData.khach_hang_id,
        type: 'thu',
        amount: values.amount,
        payment_method: values.payment_method,
        note: values.note || `Thanh toán đơn hàng ${orderData.ma_don_hang}`,
        order_id: orderData.id,
        is_partial_payment: paymentType === 'partial'
      };

      await onSubmit(paymentData);
      form.resetFields();
    } catch (error) {
      console.error('Error submitting payment:', error);
    }
  };

  // Payment method options
  const paymentMethods = [
    { value: 'cash', label: 'Tiền mặt', icon: <DollarOutlined /> },
    { value: 'transfer', label: 'Chuyển khoản', icon: <BankOutlined /> },
    { value: 'card', label: 'Thẻ tín dụng', icon: <CreditCardOutlined /> },
    { value: 'other', label: 'Khác', icon: <WalletOutlined /> }
  ];

  return (
    <Modal
      title={
        <Space>
          <DollarOutlined />
          <span>Thanh toán đơn hàng</span>
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      {orderData && (
        <>
          {/* Thông tin đơn hàng */}
          <Card size="small" style={{ marginBottom: 16, backgroundColor: '#f8f9fa' }}>
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>Mã đơn hàng: </Text>
                <Text>{orderData.ma_don_hang}</Text>
              </Col>
              <Col span={12}>
                <Text strong>Khách hàng: </Text>
                <Text>{orderData.ten_khach_hang || 'Khách lẻ'}</Text>
              </Col>
            </Row>
            <Divider style={{ margin: '8px 0' }} />
            <Row gutter={16}>
              <Col span={8}>
                <Text type="secondary">Tổng tiền:</Text>
                <br />
                <Text strong style={{ color: '#1890ff', fontSize: 16 }}>
                  {totalAmount.toLocaleString('vi-VN')}đ
                </Text>
              </Col>
              <Col span={8}>
                <Text type="secondary">Đã thanh toán:</Text>
                <br />
                <Text strong style={{ color: '#52c41a', fontSize: 16 }}>
                  {paidAmount.toLocaleString('vi-VN')}đ
                </Text>
              </Col>
              <Col span={8}>
                <Text type="secondary">Còn nợ:</Text>
                <br />
                <Text strong style={{ color: '#f5222d', fontSize: 16 }}>
                  {remainingAmount.toLocaleString('vi-VN')}đ
                </Text>
              </Col>
            </Row>
          </Card>

          {/* Form thanh toán */}
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
            {/* Loại thanh toán */}
            <Form.Item label="Loại thanh toán">
              <Radio.Group 
                value={paymentType} 
                onChange={handlePaymentTypeChange}
                style={{ width: '100%' }}
              >
                <Radio.Button value="full" style={{ width: '50%', textAlign: 'center' }}>
                  Thanh toán toàn bộ
                </Radio.Button>
                <Radio.Button value="partial" style={{ width: '50%', textAlign: 'center' }}>
                  Thanh toán một phần
                </Radio.Button>
              </Radio.Group>
            </Form.Item>

            {/* Số tiền thanh toán */}
            <Form.Item
              label="Số tiền thanh toán"
              name="amount"
              rules={[
                { required: true, message: 'Vui lòng nhập số tiền thanh toán' },
                { 
                  validator: (_, value) => {
                    if (value <= 0) {
                      return Promise.reject('Số tiền phải lớn hơn 0');
                    }
                    if (value > remainingAmount) {
                      return Promise.reject('Số tiền không được vượt quá số tiền còn nợ');
                    }
                    return Promise.resolve();
                  }
                }
              ]}
            >
              <InputNumber
                style={{ width: '100%' }}
                formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={value => value.replace(/\$\s?|(,*)/g, '')}
                placeholder="Nhập số tiền thanh toán"
                min={0}
                max={remainingAmount}
                disabled={paymentType === 'full'}
                addonAfter="VND"
              />
            </Form.Item>

            {/* Phương thức thanh toán */}
            <Form.Item
              label="Phương thức thanh toán"
              name="payment_method"
              rules={[{ required: true, message: 'Vui lòng chọn phương thức thanh toán' }]}
            >
              <Select placeholder="Chọn phương thức thanh toán">
                {paymentMethods.map(method => (
                  <Option key={method.value} value={method.value}>
                    <Space>
                      {method.icon}
                      {method.label}
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            {/* Ghi chú */}
            <Form.Item
              label="Ghi chú"
              name="note"
            >
              <TextArea
                rows={3}
                placeholder="Nhập ghi chú cho thanh toán (tùy chọn)"
              />
            </Form.Item>

            {/* Buttons */}
            <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
              <Space>
                <Button onClick={onCancel}>
                  Hủy
                </Button>
                <Button 
                  type="primary" 
                  htmlType="submit"
                  loading={loading}
                  icon={<DollarOutlined />}
                >
                  Xác nhận thanh toán
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </>
      )}
    </Modal>
  );
};

export default PaymentModal;
