import axios from 'axios';
import { message } from 'antd';
import Cookies from 'js-cookie';

// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  TEST_BASE_URL: import.meta.env.VITE_TEST_API_URL || 'http://localhost:5000/api/test',
  TIMEOUT: 10000,
};

// Helper function to build API URLs
export const buildApiUrl = (endpoint, isTestApi = false) => {
  const baseUrl = isTestApi ? API_CONFIG.TEST_BASE_URL : API_CONFIG.BASE_URL;
  return `${baseUrl}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
};

// Helper function for fetch requests
export const apiRequest = async (url, options = {}) => {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
};

// Create axios instance
const api = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // Unauthorized - redirect to login
          Cookies.remove('token');
          if (window.location.pathname !== '/login') {
            message.error('Phiên đăng nhập đã hết hạn');
            window.location.href = '/login';
          }
          break;
        
        case 403:
          // Forbidden
          message.error(data.message || 'Bạn không có quyền thực hiện hành động này');
          break;
        
        case 404:
          // Not found
          message.error(data.message || 'Không tìm thấy tài nguyên');
          break;
        
        case 422:
          // Validation error
          if (data.errors && Array.isArray(data.errors)) {
            data.errors.forEach(err => {
              message.error(`${err.field}: ${err.message}`);
            });
          } else {
            message.error(data.message || 'Dữ liệu không hợp lệ');
          }
          break;
        
        case 500:
          // Server error
          message.error('Lỗi máy chủ, vui lòng thử lại sau');
          break;
        
        default:
          message.error(data.message || 'Có lỗi xảy ra');
      }
    } else if (error.request) {
      // Network error
      message.error('Không thể kết nối đến máy chủ');
    } else {
      // Other error
      message.error('Có lỗi xảy ra');
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  getMe: () => api.get('/auth/me'),
  changePassword: (passwordData) => api.post('/auth/change-password', passwordData),
};

// Users API
export const usersAPI = {
  getUsers: (params) => api.get('/users', { params }),
  getUser: (id) => api.get(`/users/${id}`),
  createUser: (userData) => api.post('/users', userData),
  updateUser: (id, userData) => api.put(`/users/${id}`, userData),
  deleteUser: (id) => api.delete(`/users/${id}`),
  assignRoles: (id, roleIds) => api.post(`/users/${id}/assign-roles`, { vai_tro_ids: roleIds }),
};

// Roles API
export const rolesAPI = {
  getRoles: () => api.get('/roles'),
  getRole: (id) => api.get(`/roles/${id}`),
  createRole: (roleData) => api.post('/roles', roleData),
  updateRole: (id, roleData) => api.put(`/roles/${id}`, roleData),
  deleteRole: (id) => api.delete(`/roles/${id}`),
  assignPermissions: (id, permissionIds) => api.post(`/roles/${id}/assign-permissions`, { quyen_ids: permissionIds }),
};

// Permissions API
export const permissionsAPI = {
  getPermissions: () => api.get('/permissions'),
  getPermission: (id) => api.get(`/permissions/${id}`),
  createPermission: (permissionData) => api.post('/permissions', permissionData),
  updatePermission: (id, permissionData) => api.put(`/permissions/${id}`, permissionData),
  deletePermission: (id) => api.delete(`/permissions/${id}`),
};

// Products API
export const productsAPI = {
  getProducts: (params) => api.get('/products', { params }),
  getProduct: (id) => api.get(`/products/${id}`),
  createProduct: (productData) => api.post('/products', productData),
  updateProduct: (id, productData) => api.put(`/products/${id}`, productData),
  deleteProduct: (id) => api.delete(`/products/${id}`),

  // Product Categories
  getCategories: () => api.get('/products/categories'),
  getCategory: (id) => api.get(`/products/categories/${id}`),
  createCategory: (categoryData) => api.post('/products/categories', categoryData),
  updateCategory: (id, categoryData) => api.put(`/products/categories/${id}`, categoryData),
  deleteCategory: (id) => api.delete(`/products/categories/${id}`),

  // Product Brands
  getBrands: () => api.get('/products/brands'),
  getBrand: (id) => api.get(`/products/brands/${id}`),
  createBrand: (brandData) => api.post('/products/brands', brandData),
  updateBrand: (id, brandData) => api.put(`/products/brands/${id}`, brandData),
  deleteBrand: (id) => api.delete(`/products/brands/${id}`),

  // Product Tags
  getTags: () => api.get('/products/tags'),
  getTag: (id) => api.get(`/products/tags/${id}`),
  createTag: (tagData) => api.post('/products/tags', tagData),
  updateTag: (id, tagData) => api.put(`/products/tags/${id}`, tagData),
  deleteTag: (id) => api.delete(`/products/tags/${id}`),

  // Product Attributes
  getAttributes: () => api.get('/products/attributes'),
  getAttribute: (id) => api.get(`/products/attributes/${id}`),
  createAttribute: (attributeData) => api.post('/products/attributes', attributeData),
  updateAttribute: (id, attributeData) => api.put(`/products/attributes/${id}`, attributeData),
  deleteAttribute: (id) => api.delete(`/products/attributes/${id}`),

  // Product Variants
  getVariants: (productId) => api.get(`/products/${productId}/variants`),
  getVariant: (productId, variantId) => api.get(`/products/${productId}/variants/${variantId}`),
  createVariant: (productId, variantData) => api.post(`/products/${productId}/variants`, variantData),
  updateVariant: (productId, variantId, variantData) => api.put(`/products/${productId}/variants/${variantId}`, variantData),
  deleteVariant: (productId, variantId) => api.delete(`/products/${productId}/variants/${variantId}`),
};

// Orders API
export const ordersAPI = {
  getOrders: (params) => api.get('/orders', { params }),
  getOrder: (id) => api.get(`/orders/${id}`),
  createOrder: (orderData) => api.post('/orders', orderData),
  updateOrder: (id, orderData) => api.put(`/orders/${id}`, orderData),
  deleteOrder: (id) => api.delete(`/orders/${id}`),

  // Order Details
  getOrderDetails: (id) => api.get(`/orders/${id}/details`),

  // Order Status Management
  updateOrderStatus: (id, status) => api.put(`/orders/${id}/status`, { trang_thai: status }),
  cancelOrder: (id) => api.put(`/orders/${id}/cancel`),
  completeOrder: (id) => api.put(`/orders/${id}/complete`),

  // Order Statistics
  getOrderStats: (params) => api.get('/orders/stats', { params }),
};

// Test API endpoints (temporary for development)
export const testAPI = {
  // Orders
  getOrders: (params) => {
    const cleanParams = Object.fromEntries(
      Object.entries(params).filter(([_, value]) => value !== undefined && value !== 'undefined' && value !== '')
    );
    const queryString = new URLSearchParams(cleanParams).toString();
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/orders${queryString ? '?' + queryString : ''}`;
    return fetch(url).then(res => res.json());
  },

  getOrder: (id) => {
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/orders/${id}`;
    return fetch(url).then(res => res.json());
  },

  createOrder: (orderData) => {
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/orders`;
    return fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(orderData)
    }).then(res => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }
      return res.json();
    });
  },

  updateOrder: (id, orderData) => {
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/orders/${id}`;
    return fetch(url, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(orderData)
    }).then(res => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }
      return res.json();
    });
  },

  updateOrderStatus: (id, status) => {
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/orders/${id}/status`;
    return fetch(url, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ trang_thai: status })
    }).then(res => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }
      return res.json();
    });
  },

  // Products
  getProducts: (params) => {
    const queryString = new URLSearchParams(params).toString();
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/products${queryString ? '?' + queryString : ''}`;
    return fetch(url).then(res => res.json());
  },

  getProductVariants: (params) => {
    const queryString = new URLSearchParams(params).toString();
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/product-variants${queryString ? '?' + queryString : ''}`;
    return fetch(url).then(res => {
      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }
      return res.json();
    });
  },

  getProductStockHistory: (productId, params) => {
    const queryString = new URLSearchParams(params).toString();
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/products/${productId}/stock-history${queryString ? '?' + queryString : ''}`;
    return fetch(url).then(res => res.json());
  },

  getVariantStockHistory: (variantId, params) => {
    const queryString = new URLSearchParams(params).toString();
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/variants/${variantId}/stock-history${queryString ? '?' + queryString : ''}`;
    return fetch(url).then(res => res.json());
  },

  // Categories
  getCategories: () => {
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/categories`;
    return fetch(url).then(res => res.json());
  },

  createCategory: (data) => {
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/categories`;
    return fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    }).then(res => res.json());
  },

  updateCategory: (id, data) => {
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/categories/${id}`;
    return fetch(url, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    }).then(res => res.json());
  },

  deleteCategory: (id) => {
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/categories/${id}`;
    return fetch(url, {
      method: 'DELETE'
    }).then(res => res.json());
  },

  // Brands
  getBrands: () => {
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/brands`;
    return fetch(url).then(res => res.json());
  },

  createBrand: (data) => {
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/brands`;
    return fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    }).then(res => res.json());
  },

  updateBrand: (id, data) => {
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/brands/${id}`;
    return fetch(url, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    }).then(res => res.json());
  },

  deleteBrand: (id) => {
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/brands/${id}`;
    return fetch(url, {
      method: 'DELETE'
    }).then(res => res.json());
  },

  // Customers
  getCustomers: (params) => {
    const queryString = new URLSearchParams(params).toString();
    const url = `${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/test/customers${queryString ? '?' + queryString : ''}`;
    return fetch(url).then(res => res.json());
  },
};

// Warehouses API
export const warehousesAPI = {
  getWarehouses: (params) => api.get('/warehouses', { params }),
  getWarehouse: (id) => api.get(`/warehouses/${id}`),
  createWarehouse: (warehouseData) => api.post('/warehouses', warehouseData),
  updateWarehouse: (id, warehouseData) => api.put(`/warehouses/${id}`, warehouseData),
  deleteWarehouse: (id) => api.delete(`/warehouses/${id}`),

  // Inventory Management
  getInventory: (params) => api.get('/warehouses/inventory', { params }),
  getInventoryByWarehouse: (warehouseId, params) => api.get(`/warehouses/${warehouseId}/inventory`, { params }),
  adjustInventory: (adjustmentData) => api.post('/warehouses/inventory/adjust', adjustmentData),

  // Stock Movements
  getStockMovements: (params) => api.get('/warehouses/movements', { params }),
  getStockMovement: (id) => api.get(`/warehouses/movements/${id}`),
  createStockMovement: (movementData) => api.post('/warehouses/movements', movementData),

  // Stock Checks
  getStockChecks: (params) => api.get('/warehouses/stock-checks', { params }),
  getStockCheck: (id) => api.get(`/warehouses/stock-checks/${id}`),
  createStockCheck: (checkData) => api.post('/warehouses/stock-checks', checkData),
  updateStockCheck: (id, checkData) => api.put(`/warehouses/stock-checks/${id}`, checkData),
  deleteStockCheck: (id) => api.delete(`/warehouses/stock-checks/${id}`),

  // Stock Check Details
  getStockCheckDetails: (id, params) => api.get(`/warehouses/stock-checks/${id}/details`, { params }),
  updateStockCheckItem: (stockCheckId, itemId, data) => api.put(`/warehouses/stock-checks/${stockCheckId}/items/${itemId}`, data),
  completeStockCheck: (id) => api.post(`/warehouses/stock-checks/${id}/complete`),

  // Stock Check Products
  getAvailableProducts: (id, params) => api.get(`/warehouses/stock-checks/${id}/available-products`, { params }),
  addProductToStockCheck: (id, data) => api.post(`/warehouses/stock-checks/${id}/products`, data),
  removeProductFromStockCheck: (stockCheckId, itemId) => api.delete(`/warehouses/stock-checks/${stockCheckId}/items/${itemId}`),
};

//Customer API
export const customerAPI = {
  getAllCustomerGroup: (params) => api.get('/customer-groups', { params }),
  getCustomerGroups: () => api.get('/customer-groups/all'),
  createCustomerGroup: (data) => api.post('/customer-groups', data),
  updateCustomerGroup: (id, data) => api.put(`/customer-groups/${id}`, data),
  deleteCustomerGroup: (id) => api.delete(`/customer-groups/${id}`),

  getCustomers: (params) => api.get('/customers', { params }),
  getCustomer: (id) => api.get(`/customers/${id}`),
  createCustomer: (customerData) => api.post('/customers', customerData),
  updateCustomer: (id, customerData) => api.put(`/customers/${id}`, customerData),
  deleteCustomer: (id) => api.delete(`/customers/${id}`),
  exportCustomers: () => api.get('/customers/export', { responseType: 'blob' }),
  importCustomers: (formData) => api.post('/customers/import', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }),
}

// Customers API (alias for consistency)
export const customersAPI = customerAPI;

// Health check API
export const healthAPI = {
  check: () => api.get('/health'),
};

export default api;
