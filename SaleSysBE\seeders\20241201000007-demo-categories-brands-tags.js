'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Insert categories
    await queryInterface.bulkInsert('loai_san_pham', [
      {
        ten: 'Áo thun',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Quần jean',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Giày dép',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: '<PERSON>ụ kiện',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Áo khoác',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      }
    ], {});

    // Insert brands
    await queryInterface.bulkInsert('nhan_hieu', [
      {
        ten: 'Nike',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Adidas',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Uniqlo',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Zara',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'H&M',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      }
    ], {});

    // Insert tags
    await queryInterface.bulkInsert('tag', [
      {
        ten: 'Mới nhất',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Bán chạy',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Giảm giá',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Cao cấp',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        ten: 'Phổ biến',
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('tag', null, {});
    await queryInterface.bulkDelete('nhan_hieu', null, {});
    await queryInterface.bulkDelete('loai_san_pham', null, {});
  }
};
