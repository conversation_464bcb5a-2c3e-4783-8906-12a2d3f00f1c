import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![stop](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0yODguNSA2ODIuOEwyNzcuNyAyMjRDMjU4IDI0MCAyNDAgMjU4IDIyNCAyNzcuN2w1MjIuOCA1MjIuOEM2ODIuOCA4NTIuNyA2MDEgODg0IDUxMiA4ODRjLTIwNS40IDAtMzcyLTE2Ni42LTM3Mi0zNzJzMTY2LjYtMzcyIDM3Mi0zNzIgMzcyIDE2Ni42IDM3MiAzNzJjMCA4OS0zMS4zIDE3MC44LTgzLjUgMjM0Ljh6IiBmaWxsPSIjMTY3N2ZmIiAvPjxwYXRoIGQ9Ik01MTIgMTQwYy0yMDUuNCAwLTM3MiAxNjYuNi0zNzIgMzcyczE2Ni42IDM3MiAzNzIgMzcyYzg5IDAgMTcwLjgtMzEuMyAyMzQuOC04My41TDIyNCAyNzcuN2MxNi0xOS43IDM0LTM3LjcgNTMuNy01My43bDUyMi44IDUyMi44Qzg1Mi43IDY4Mi44IDg4NCA2MDEgODg0IDUxMmMwLTIwNS40LTE2Ni42LTM3Mi0zNzItMzcyeiIgZmlsbD0iI2U2ZjRmZiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
