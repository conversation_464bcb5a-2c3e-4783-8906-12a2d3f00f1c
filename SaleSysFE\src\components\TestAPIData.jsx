import React from 'react';
import { Card, Spin, Alert, Collapse, Tag } from 'antd';
import { useCategories, useBrands, useTags, useAttributes } from '../hooks/useProducts';

const { Panel } = Collapse;

const TestAPIData = () => {
  const { data: categories = [], isLoading: categoriesLoading, error: categoriesError } = useCategories();
  const { data: brands = [], isLoading: brandsLoading, error: brandsError } = useBrands();
  const { data: tags = [], isLoading: tagsLoading, error: tagsError } = useTags();
  const { data: attributes = [], isLoading: attributesLoading, error: attributesError } = useAttributes();

  const renderDataSection = (title, data, loading, error, emoji) => (
    <Panel header={`${emoji} ${title} (${data.length} items)`} key={title}>
      {loading && <Spin />}
      {error && (
        <Alert
          message="Error"
          description={error.message || 'Failed to load data'}
          type="error"
          style={{ marginBottom: 16 }}
        />
      )}
      
      <div style={{ marginBottom: 16 }}>
        <strong>Status:</strong>{' '}
        <Tag color={loading ? 'blue' : error ? 'red' : 'green'}>
          {loading ? 'Loading' : error ? 'Error' : 'Success'}
        </Tag>
      </div>

      <div style={{ marginBottom: 16 }}>
        <strong>Data Type:</strong> {Array.isArray(data) ? 'Array' : typeof data}
        <br />
        <strong>Length:</strong> {data.length}
      </div>

      {data.length > 0 && (
        <div>
          <strong>Sample Data:</strong>
          <div style={{ background: '#f5f5f5', padding: 12, borderRadius: 4, marginTop: 8 }}>
            <pre style={{ fontSize: 12, margin: 0 }}>
              {JSON.stringify(data.slice(0, 2), null, 2)}
            </pre>
          </div>
        </div>
      )}

      {data.length === 0 && !loading && !error && (
        <Alert
          message="No Data"
          description="No items found in this category"
          type="warning"
        />
      )}
    </Panel>
  );

  return (
    <Card title="🧪 Test API Data Loading" style={{ maxWidth: 1000, margin: '20px auto' }}>
      <Alert
        message="API Data Test"
        description="This component tests the loading of categories, brands, tags, and attributes from the API. Check console for detailed logs."
        type="info"
        style={{ marginBottom: 20 }}
      />

      <Collapse defaultActiveKey={['Categories']}>
        {renderDataSection('Categories', categories, categoriesLoading, categoriesError, '🏷️')}
        {renderDataSection('Brands', brands, brandsLoading, brandsError, '🏢')}
        {renderDataSection('Tags', tags, tagsLoading, tagsError, '🔖')}
        {renderDataSection('Attributes', attributes, attributesLoading, attributesError, '🔧')}
      </Collapse>

      <div style={{ marginTop: 20, fontSize: 12, color: '#666' }}>
        <p><strong>Debug Info:</strong></p>
        <ul>
          <li>Check browser console for detailed API response logs</li>
          <li>Look for logs starting with 🏷️, 🏢, 🔖, 🔧</li>
          <li>Verify data structure and content</li>
          <li>Check for any error messages</li>
        </ul>
      </div>
    </Card>
  );
};

export default TestAPIData;
