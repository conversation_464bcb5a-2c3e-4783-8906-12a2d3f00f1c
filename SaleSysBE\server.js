require('dotenv').config();
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const path = require('path');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const roleRoutes = require('./routes/roles');
const permissionRoutes = require('./routes/permissions');
const productRoutes = require('./routes/products');
const orderRoutes = require('./routes/orders');
const customerRoutes = require('./routes/customers');
const warehouseRoutes = require('./routes/warehouses');
const uploadRoutes = require('./routes/uploadRoutes');
const customerGroupRoutes = require('./routes/customerGroup');
const tagRoutes = require('./routes/tags');
const employeeRoutes = require('./routes/employees');

// Import middleware
const { authenticateToken } = require('./middleware/auth');
const { errorHandler } = require('./middleware/errorHandler');

// Import database
const db = require('./models');

const app = express();
const PORT = process.env.PORT || 5000;

// Security middleware
app.use(helmet());

// Rate limiting
// const limiter = rateLimit({
//   windowMs: 15 * 60 * 1000, // 15 minutes
//   max: 100 // limit each IP to 100 requests per windowMs
// });
// app.use(limiter);
app.get('/', (req, res) => {
  res.status(200).send('Server is running');
});
app.use(cors({
  origin: '*', // Cho phép mọi nguồn
  credentials: true // Cho phép gửi cookie hoặc thông tin xác thực
}));
// CORS configuration
// app.use(cors({
//   origin: process.env.FRONTEND_URL || 'http://localhost:3000',
//   credentials: true
// }));

// Logging
app.use(morgan('combined'));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  });
});

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', authenticateToken, userRoutes);
app.use('/api/roles', authenticateToken, roleRoutes);
app.use('/api/permissions', authenticateToken, permissionRoutes);
app.use('/api/products', authenticateToken, productRoutes);
app.use('/api/orders', authenticateToken, orderRoutes);
app.use('/api/customers', authenticateToken, customerRoutes);
app.use('/api/warehouses', authenticateToken, warehouseRoutes);
app.use('/api/upload', uploadRoutes); // Upload routes không cần auth để test
app.use('/api/customer-groups', authenticateToken, customerGroupRoutes);
app.use('/api/tags', authenticateToken, tagRoutes);
app.use('/api/employees', authenticateToken, employeeRoutes);

// Test route for products without auth
app.get('/api/test/products', async (req, res) => {
  try {
    // Import controller directly
    const { getProducts } = require('./controllers/productController');
    // Set fake user for testing
    req.user = { username: 'test' };
    await getProducts(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for single product without auth
app.get('/api/test/products/:id', async (req, res) => {
  try {
    // Import controller directly
    const { getProduct } = require('./controllers/productController');
    // Set fake user for testing
    req.user = { username: 'test' };
    await getProduct(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for inventory without auth
app.get('/api/test/inventory', async (req, res) => {
  try {
    // Import controller directly
    const { getInventory } = require('./controllers/warehouseController');
    // Set fake user for testing
    req.user = { username: 'test' };
    await getInventory(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route to check all product variants
app.get('/api/test/all-variants', async (req, res) => {
  try {
    const { PhienBanSanPham, SanPham, TonKhoPhienBan, KhoHang } = require('./models');

    // Get all product variants
    const allVariants = await PhienBanSanPham.findAll({
      include: [
        {
          model: SanPham,
          as: 'sanPham',
          attributes: ['ten', 'ma']
        },
        {
          model: TonKhoPhienBan,
          as: 'tonKhoList',
          include: [
            {
              model: KhoHang,
              as: 'khoHang',
              attributes: ['ten_kho']
            }
          ],
          required: false // LEFT JOIN to include variants without inventory
        }
      ],
      order: [['ngay_tao', 'DESC']]
    });

    const variantsWithInventory = allVariants.filter(v => v.tonKhoList && v.tonKhoList.length > 0);
    const variantsWithoutInventory = allVariants.filter(v => !v.tonKhoList || v.tonKhoList.length === 0);

    res.json({
      success: true,
      data: {
        total_variants: allVariants.length,
        variants_with_inventory: variantsWithInventory.length,
        variants_without_inventory: variantsWithoutInventory.length,
        variants_without_inventory_list: variantsWithoutInventory.map(v => ({
          id: v.id,
          ten_phien_ban: v.ten_phien_ban,
          ma: v.ma,
          san_pham: v.sanPham?.ten || 'N/A'
        }))
      }
    });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for stock checks
app.get('/api/test/stock-checks', async (req, res) => {
  try {
    const { getStockChecks } = require('./controllers/stockCheckController');
    req.user = { username: 'test' };
    await getStockChecks(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for creating stock check
app.post('/api/test/stock-checks', async (req, res) => {
  try {
    const { createStockCheck } = require('./controllers/stockCheckController');
    req.user = { username: 'test', ho_ten: 'Test User' };
    await createStockCheck(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for stock check details
app.get('/api/test/stock-checks/:id/details', async (req, res) => {
  try {
    const { getStockCheckDetails } = require('./controllers/stockCheckController');
    req.user = { username: 'test' };
    await getStockCheckDetails(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for adding products to stock check
app.post('/api/test/stock-checks/:id/products', async (req, res) => {
  try {
    const { addProductToStockCheck } = require('./controllers/stockCheckController');
    req.user = { username: 'test' };
    await addProductToStockCheck(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for available products
app.get('/api/test/stock-checks/:id/available-products', async (req, res) => {
  try {
    const { getAvailableProducts } = require('./controllers/stockCheckController');
    req.user = { username: 'test' };
    await getAvailableProducts(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for warehouses
app.get('/api/test/warehouses', async (req, res) => {
  try {
    const { getWarehouses } = require('./controllers/warehouseController');
    req.user = { username: 'test' };
    await getWarehouses(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for categories
app.get('/api/test/categories', async (req, res) => {
  try {
    const { getCategories } = require('./controllers/productController');
    req.user = { username: 'test' };
    await getCategories(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for brands
app.get('/api/test/brands', async (req, res) => {
  try {
    const { getBrands } = require('./controllers/productController');
    req.user = { username: 'test' };
    await getBrands(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test routes for categories CRUD
app.post('/api/test/categories', async (req, res) => {
  try {
    const { createCategory } = require('./controllers/productController');
    req.user = { username: 'test' };
    await createCategory(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

app.put('/api/test/categories/:id', async (req, res) => {
  try {
    const { updateCategory } = require('./controllers/productController');
    req.user = { username: 'test' };
    await updateCategory(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

app.delete('/api/test/categories/:id', async (req, res) => {
  try {
    const { deleteCategory } = require('./controllers/productController');
    req.user = { username: 'test' };
    await deleteCategory(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test routes for brands CRUD
app.post('/api/test/brands', async (req, res) => {
  try {
    const { createBrand } = require('./controllers/productController');
    req.user = { username: 'test' };
    await createBrand(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

app.put('/api/test/brands/:id', async (req, res) => {
  try {
    const { updateBrand } = require('./controllers/productController');
    req.user = { username: 'test' };
    await updateBrand(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

app.delete('/api/test/brands/:id', async (req, res) => {
  try {
    const { deleteBrand } = require('./controllers/productController');
    req.user = { username: 'test' };
    await deleteBrand(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for product stock history
app.get('/api/test/products/:productId/stock-history', async (req, res) => {
  try {
    const { getProductStockHistory } = require('./controllers/warehouseController');
    req.user = { username: 'test' };
    await getProductStockHistory(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for variant stock history
app.get('/api/test/variants/:variantId/stock-history', async (req, res) => {
  try {
    const { getVariantStockHistory } = require('./controllers/warehouseController');
    req.user = { username: 'test' };
    await getVariantStockHistory(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for customers
app.get('/api/test/customers', async (req, res) => {
  try {
    const { getCustomers } = require('./controllers/customerController');
    req.user = { username: 'test' };
    await getCustomers(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for product variants (for order creation)
app.get('/api/test/product-variants', async (req, res) => {
  try {
    const { PhienBanSanPham, SanPham, TonKhoPhienBan, KhoHang } = require('./models');
    const { Op } = require('sequelize');

    const { search, limit = 50 } = req.query;

    let whereCondition = {};
    if (search) {
      whereCondition = {
        [Op.or]: [
          { '$sanPham.ten$': { [Op.like]: `%${search}%` } },
          { 'ten_phien_ban': { [Op.like]: `%${search}%` } },
          { 'ma': { [Op.like]: `%${search}%` } }
        ]
      };
    }

    const variants = await PhienBanSanPham.findAll({
      where: whereCondition,
      include: [
        {
          model: SanPham,
          as: 'sanPham',
          attributes: ['ten']
        },
        {
          model: TonKhoPhienBan,
          as: 'tonKhoList',
          include: [{
            model: KhoHang,
            as: 'khoHang',
            attributes: ['ten_kho']
          }],
          required: false
        }
      ],
      limit: parseInt(limit),
      order: [['ngay_tao', 'DESC']]
    });

    // Calculate total stock for each variant
    const variantsWithStock = variants.map(variant => {
      const totalStock = variant.tonKhoList?.reduce((sum, stock) => sum + (stock.so_luong || 0), 0) || 0;

      return {
        id: variant.id,
        ten_san_pham: variant.sanPham?.ten,
        ten_phien_ban: variant.ten_phien_ban,
        ma_sku: variant.ma,
        anh: variant.anh,
        gia_ban_le: variant.gia_le,
        gia_ban_buon: variant.gia_buon,
        gia_nhap: variant.gia_nhap,
        ton_kho: totalStock
      };
    });

    res.json({
      success: true,
      data: variantsWithStock,
      total: variantsWithStock.length
    });
  } catch (error) {
    console.error('Error fetching product variants:', error);
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for orders
app.get('/api/test/orders', async (req, res) => {
  try {
    const { getOrders } = require('./controllers/orderController');
    req.user = { username: 'test', id: 1 };
    await getOrders(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for get order by ID
app.get('/api/test/orders/:id', async (req, res) => {
  try {
    const { getOrder } = require('./controllers/orderController');
    req.user = { username: 'test', id: 1 };
    await getOrder(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test route for creating orders
app.post('/api/test/orders', async (req, res) => {
  try {
    const { createOrder } = require('./controllers/orderController');
    req.user = { username: 'test', id: 1, ho_ten: 'Test User' };
    await createOrder(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Test routes for debt management
app.get('/api/test/debt', async (req, res) => {
  try {
    const { getDebtList } = require('./controllers/debtController');
    req.user = { username: 'test', id: 1 };
    await getDebtList(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

app.get('/api/test/debt/customer/:customerId', async (req, res) => {
  try {
    const { getCustomerDebtDetail } = require('./controllers/debtController');
    req.user = { username: 'test', id: 1 };
    await getCustomerDebtDetail(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

app.post('/api/test/debt/payment', async (req, res) => {
  try {
    const { createPayment } = require('./controllers/debtController');
    req.user = { username: 'test', id: 1 };
    await createPayment(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

app.get('/api/test/debt/report', async (req, res) => {
  try {
    const { getDebtReport } = require('./controllers/debtController');
    req.user = { username: 'test', id: 1 };
    await getDebtReport(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

app.post('/api/test/debt/notes', async (req, res) => {
  try {
    const { createInternalNote } = require('./controllers/debtController');
    req.user = { username: 'test', id: 1 };
    await createInternalNote(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

app.get('/api/test/debt/notes/:customerId', async (req, res) => {
  try {
    const { getInternalNotes } = require('./controllers/debtController');
    req.user = { username: 'test', id: 1 };
    await getInternalNotes(req, res);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
});

// Error handling middleware
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ message: 'Route not found' });
});

// Database connection and server start
async function startServer() {
  try {
    // Test database connection
    await db.sequelize.authenticate();
    console.log('✅ Database connection established successfully.');

    // Note: We're using migrations instead of sync for better control
    console.log('✅ Using migrations for database schema management.');

    // Start server
    app.listen(PORT, () => {
      console.log(`🚀 Server is running on port ${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV}`);
      console.log(`🔗 API URL: http://localhost:${PORT}/api`);
    });
  } catch (error) {
    console.error('❌ Unable to start server:', error);
    process.exit(1);
  }
}

startServer();

module.exports = app;
