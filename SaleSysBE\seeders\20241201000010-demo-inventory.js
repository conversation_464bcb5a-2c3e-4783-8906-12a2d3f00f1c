'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Insert inventory data
    await queryInterface.bulkInsert('ton_kho_phien_ban', [
      // <PERSON>o thun Nike - <PERSON>ho ch<PERSON>h
      {
        kho_hang_id: 1, // Kho chính
        phien_ban_san_pham_id: 1, // AT001-S-RED
        so_luong_ton: 50,
        so_luong_toi_thieu: 10,
        gia_von: 200000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        kho_hang_id: 1, // Kho chính
        phien_ban_san_pham_id: 2, // AT001-M-RED
        so_luong_ton: 75,
        so_luong_toi_thieu: 15,
        gia_von: 200000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        kho_hang_id: 1, // Kho chính
        phien_ban_san_pham_id: 3, // AT001-L-RED
        so_luong_ton: 30,
        so_luong_toi_thieu: 10,
        gia_von: 200000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      
      // Áo thun Nike - Kho phụ Quận 2
      {
        kho_hang_id: 2, // Kho phụ Quận 2
        phien_ban_san_pham_id: 1, // AT001-S-RED
        so_luong_ton: 25,
        so_luong_toi_thieu: 5,
        gia_von: 200000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        kho_hang_id: 2, // Kho phụ Quận 2
        phien_ban_san_pham_id: 2, // AT001-M-RED
        so_luong_ton: 40,
        so_luong_toi_thieu: 8,
        gia_von: 200000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },

      // Quần jean Adidas - Kho chính
      {
        kho_hang_id: 1, // Kho chính
        phien_ban_san_pham_id: 4, // QJ001-30-BLUE
        so_luong_ton: 20,
        so_luong_toi_thieu: 5,
        gia_von: 600000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        kho_hang_id: 1, // Kho chính
        phien_ban_san_pham_id: 5, // QJ001-32-BLUE
        so_luong_ton: 35,
        so_luong_toi_thieu: 8,
        gia_von: 600000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },

      // Quần jean Adidas - Kho Hà Nội
      {
        kho_hang_id: 3, // Kho Hà Nội
        phien_ban_san_pham_id: 4, // QJ001-30-BLUE
        so_luong_ton: 15,
        so_luong_toi_thieu: 3,
        gia_von: 600000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        kho_hang_id: 3, // Kho Hà Nội
        phien_ban_san_pham_id: 5, // QJ001-32-BLUE
        so_luong_ton: 25,
        so_luong_toi_thieu: 5,
        gia_von: 600000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },

      // Áo khoác Uniqlo - Kho chính
      {
        kho_hang_id: 1, // Kho chính
        phien_ban_san_pham_id: 6, // AK001-M-BLACK
        so_luong_ton: 12,
        so_luong_toi_thieu: 3,
        gia_von: 900000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },

      // Giày Nike - Kho chính
      {
        kho_hang_id: 1, // Kho chính
        phien_ban_san_pham_id: 7, // GT001-40-WHITE
        so_luong_ton: 8,
        so_luong_toi_thieu: 2,
        gia_von: 1800000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        kho_hang_id: 1, // Kho chính
        phien_ban_san_pham_id: 8, // GT001-41-WHITE
        so_luong_ton: 10,
        so_luong_toi_thieu: 2,
        gia_von: 1800000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },

      // Giày Nike - Kho Đà Nẵng
      {
        kho_hang_id: 4, // Kho Đà Nẵng
        phien_ban_san_pham_id: 7, // GT001-40-WHITE
        so_luong_ton: 5,
        so_luong_toi_thieu: 1,
        gia_von: 1800000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        kho_hang_id: 4, // Kho Đà Nẵng
        phien_ban_san_pham_id: 8, // GT001-41-WHITE
        so_luong_ton: 7,
        so_luong_toi_thieu: 1,
        gia_von: 1800000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },

      // Túi xách Zara - Kho chính
      {
        kho_hang_id: 1, // Kho chính
        phien_ban_san_pham_id: 9, // TX001-M-BROWN
        so_luong_ton: 18,
        so_luong_toi_thieu: 5,
        gia_von: 1100000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },

      // Túi xách Zara - Kho phụ Quận 2
      {
        kho_hang_id: 2, // Kho phụ Quận 2
        phien_ban_san_pham_id: 9, // TX001-M-BROWN
        so_luong_ton: 12,
        so_luong_toi_thieu: 3,
        gia_von: 1100000,
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      }
    ], {});

    // Insert some stock history
    await queryInterface.bulkInsert('lich_su_kho', [
      {
        phien_ban_san_pham_id: 1, // AT001-S-RED
        kho_hang_id: 1, // Kho chính
        loai_giao_dich: 'nhap',
        so_luong_truoc: 0,
        so_luong_thay_doi: 50,
        so_luong_sau: 50,
        gia_von: 200000,
        ly_do: 'Nhập hàng đầu kỳ',
        nguoi_thuc_hien: 'admin',
        ngay_thuc_hien: new Date(),
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        phien_ban_san_pham_id: 2, // AT001-M-RED
        kho_hang_id: 1, // Kho chính
        loai_giao_dich: 'nhap',
        so_luong_truoc: 0,
        so_luong_thay_doi: 75,
        so_luong_sau: 75,
        gia_von: 200000,
        ly_do: 'Nhập hàng đầu kỳ',
        nguoi_thuc_hien: 'admin',
        ngay_thuc_hien: new Date(),
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      },
      {
        phien_ban_san_pham_id: 4, // QJ001-30-BLUE
        kho_hang_id: 1, // Kho chính
        loai_giao_dich: 'nhap',
        so_luong_truoc: 0,
        so_luong_thay_doi: 20,
        so_luong_sau: 20,
        gia_von: 600000,
        ly_do: 'Nhập hàng từ nhà cung cấp',
        nguoi_thuc_hien: 'admin',
        ngay_thuc_hien: new Date(),
        nguoi_tao: 'admin',
        ngay_tao: new Date(),
        ngay_cap_nhap: new Date()
      }
    ], {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('lich_su_kho', null, {});
    await queryInterface.bulkDelete('ton_kho_phien_ban', null, {});
  }
};
