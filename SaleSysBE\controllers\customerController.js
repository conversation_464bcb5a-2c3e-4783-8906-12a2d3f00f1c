const db = require('../models');
const { Op } = require('sequelize');
const { AppError } = require('../middleware/errorHandler');

// Sử dụng model NguoiDung thay vì <PERSON>ang
const NguoiDung = db.<PERSON>uoi<PERSON>ung;
const NhomKhachHang = db.<PERSON>hom<PERSON>ang;
const DonHang = db.<PERSON>;
const sequelize = db.sequelize;
const HoSoCaNhan = db.Ho<PERSON>o<PERSON>a<PERSON>han;
const Tag = db.Tag;
const CongNoNguoiDung = db.CongNoNguoiDung;
const PhanCongNhanVienPhuTrach = db.PhanCongNhanVienPhuTrach;
const Sequelize = require('sequelize');

/**
 * Lấy danh sách khách hàng
 */
const getCustomers = async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 10, 
      search = '', 
      trang_thai = '',
      sort_by = 'ho_ten',
      sort_order = 'asc',
      nhom_khach_hang_id = '',
      gioi_tinh = '',
      ngay_sinh = '',
      nhan_vien_id = '',
      trang_thai_list = '',
      tags = '',
      ngay_tao = '',
      khoang_ngay_sinh = '',
      ngay_sinh_cu_the = ''
    } = req.query;

    // Xây dựng điều kiện tìm kiếm
    const whereClause = {
      loai_nguoi_dung: 'khach_hang' // Chỉ lấy người dùng có loại là khách hàng
    };
    
    // Tìm kiếm theo từ khóa
    if (search) {
      whereClause[Op.or] = [
        { id: { [Op.like]: `%${search}%` } }, // Thay cho ma_khach_hang
        { ho_ten: { [Op.like]: `%${search}%` } },
        { so_dien_thoai: { [Op.like]: `%${search}%` } }
      ];
    }

    // Lọc theo trạng thái
    if (trang_thai) {
      whereClause.trang_thai = trang_thai;
    }

    // Lọc theo danh sách trạng thái
    if (trang_thai_list && Array.isArray(JSON.parse(trang_thai_list))) {
      const trangThaiArray = JSON.parse(trang_thai_list);
      if (trangThaiArray.length > 0) {
        whereClause.trang_thai = { [Op.in]: trangThaiArray };
      }
    }

    // Xác định thứ tự sắp xếp
    const order = [[sort_by, sort_order.toUpperCase()]];

    // Tính toán phân trang
    const offset = (page - 1) * limit;
    
    // Chuẩn bị các điều kiện include
    const includeConditions = [
      {
        model: NhomKhachHang,
        as: 'nhomKhachHangList',
        through: { attributes: [] }, // Không lấy thông tin bảng trung gian
        attributes: ['id', 'ten_nhom']
      },
      {
        model: HoSoCaNhan,
        as: 'hoSoCaNhan',
        attributes: ['id', 'gioi_tinh', 'ngay_sinh']
      },
      {
        model: Tag,
        as: 'tagList',
        through: { attributes: [] },
        attributes: ['id', 'ten']
      },
      {
        model: CongNoNguoiDung,
        as: 'congNo',
        attributes: ['tong_cong_no']
      }
    ];

    // Điều kiện lọc theo nhóm khách hàng
    if (nhom_khach_hang_id) {
      includeConditions[0].where = { id: nhom_khach_hang_id };
      includeConditions[0].required = true;
    }

    // Điều kiện lọc theo giới tính
    if (gioi_tinh) {
      if (!includeConditions[1].where) includeConditions[1].where = {};
      includeConditions[1].where.gioi_tinh = gioi_tinh;
      includeConditions[1].required = true;
    }

    // Điều kiện lọc theo ngày sinh cụ thể
    if (ngay_sinh_cu_the) {
      const date = new Date(ngay_sinh_cu_the);
      if (!includeConditions[1].where) includeConditions[1].where = {};
      includeConditions[1].where.ngay_sinh = date;
      includeConditions[1].required = true;
    }

    // Điều kiện lọc theo khoảng ngày sinh
    if (khoang_ngay_sinh && Array.isArray(JSON.parse(khoang_ngay_sinh)) && JSON.parse(khoang_ngay_sinh).length === 2) {
      const [startDate, endDate] = JSON.parse(khoang_ngay_sinh);
      if (startDate && endDate) {
        if (!includeConditions[1].where) includeConditions[1].where = {};
        includeConditions[1].where.ngay_sinh = {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        };
        includeConditions[1].required = true;
      }
    }

    // Điều kiện lọc theo ngày/tháng sinh (không quan tâm năm)
    if (ngay_sinh && Array.isArray(JSON.parse(ngay_sinh)) && JSON.parse(ngay_sinh).length === 2) {
      const [startDate, endDate] = JSON.parse(ngay_sinh);
      if (startDate && endDate) {
        if (!includeConditions[1].where) includeConditions[1].where = {};
        
        // Trích xuất tháng và ngày từ ngày bắt đầu và kết thúc
        const startMonth = new Date(startDate).getMonth() + 1;
        const startDay = new Date(startDate).getDate();
        const endMonth = new Date(endDate).getMonth() + 1;
        const endDay = new Date(endDate).getDate();
        
        // Sử dụng Sequelize.literal để tạo điều kiện SQL tùy chỉnh
        includeConditions[1].where = Sequelize.literal(
          `MONTH(ho_so_ca_nhan.ngay_sinh) * 100 + DAY(ho_so_ca_nhan.ngay_sinh) BETWEEN ${startMonth * 100 + startDay} AND ${endMonth * 100 + endDay}`
        );
        includeConditions[1].required = true;
      }
    }

    // Điều kiện lọc theo ngày tạo
    if (ngay_tao && Array.isArray(JSON.parse(ngay_tao)) && JSON.parse(ngay_tao).length === 2) {
      const [startDate, endDate] = JSON.parse(ngay_tao);
      if (startDate && endDate) {
        whereClause.createdAt = {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        };
      }
    }

    // Điều kiện lọc theo tags
    if (tags && Array.isArray(JSON.parse(tags)) && JSON.parse(tags).length > 0) {
      const tagIds = JSON.parse(tags);
      includeConditions[2].where = { id: { [Op.in]: tagIds } };
      includeConditions[2].required = true;
    }

    // Điều kiện lọc theo nhân viên phụ trách
    if (nhan_vien_id) {
      includeConditions.push({
        model: PhanCongNhanVienPhuTrach,
        as: 'phanCongKhachHang',
        where: { nhan_vien_id: nhan_vien_id },
        required: true
      });
    }

    // Truy vấn danh sách khách hàng
    const { count, rows } = await NguoiDung.findAndCountAll({
      where: whereClause,
      include: includeConditions,
      order,
      limit: parseInt(limit),
      offset: parseInt(offset),
      distinct: true // Đảm bảo count chính xác khi sử dụng include
    });

    // Tính toán thông tin bổ sung cho mỗi khách hàng
    const customers = await Promise.all(rows.map(async (customer) => {
      // Lấy tổng chi tiêu
      const totalSpent = await DonHang.sum('tong_tien', {
        where: { 
          khach_hang_id: customer.id,
          trang_thai: 'hoan_thanh' // Chỉ tính đơn hàng đã hoàn thành
        }
      }) || 0;

      // Lấy tổng số đơn hàng
      const orderCount = await DonHang.count({
        where: { khach_hang_id: customer.id }
      });

      // Định dạng dữ liệu trả về
      return {
        id: customer.id,
        ma_khach_hang: `C${customer.id}`, // Tạo mã khách hàng từ ID
        ho_ten: customer.ho_ten,
        so_dien_thoai: customer.so_dien_thoai,
        email: customer.email,
        trang_thai: customer.trang_thai,
        nhomKhachHang: customer.nhomKhachHangList && customer.nhomKhachHangList.length > 0 
          ? customer.nhomKhachHangList[0] 
          : null,
        gioi_tinh: customer.hoSoCaNhan?.gioi_tinh,
        ngay_sinh: customer.hoSoCaNhan?.ngay_sinh,
        tags: customer.tagList || [],
        cong_no_hien_tai: customer.congNo?.so_du_hien_tai || 0,
        tong_chi_tieu: totalSpent,
        tong_sl_don_hang: orderCount,
        createdAt: customer.createdAt
      };
    }));

    // Trả về kết quả
    res.json({
      success: true,
      data: {
        customers,
        pagination: {
          total: count,
          page: parseInt(page),
          limit: parseInt(limit)
        }
      }
    });
  } catch (error) {
    console.error('Error in getCustomers:', error);
    res.status(500).json({
      success: false,
      message: 'Lỗi khi lấy danh sách khách hàng',
      error: error.message
    });
  }
};

/**
 * Lấy thông tin chi tiết khách hàng
 */
const getCustomer = async (req, res) => {
  const { id } = req.params;

  try {
    const customer = await NguoiDung.findByPk(id, {
      include: [
        {
          model: NhomKhachHang,
          as: 'nhomKhachHangList',
          through: { attributes: [] }
        },
        {
          model: db.CongNoNguoiDung,
          as: 'congNo'
        },
        {
          model: db.HoSoCaNhan,
          as: 'hoSoCaNhan'
        },
        {
          model: db.DiaChiNguoiDung,
          as: 'diaChiList',
          where: { mac_dinh: true },
          required: false,
          limit: 1
        },
        {
          model: db.PhanCongNhanVienPhuTrach,
          as: 'phanCongKhachHang',
          required: false
        },
        {
          model: db.Tag,
          as: 'tagList',
          through: { attributes: [] },
          required: false
        }
      ]
    });

    if (!customer) {
      throw new AppError('Không tìm thấy khách hàng', 404);
    }

    // Kiểm tra xem có phải khách hàng không
    if (customer.loai_nguoi_dung !== 'khach_hang') {
      throw new AppError('Người dùng này không phải là khách hàng', 400);
    }

    // Lấy thông tin đơn hàng
    const orderStats = await DonHang.findAll({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('tong_tien')), 'tong_chi_tieu'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'tong_sl_don_hang']
      ],
      where: {
        khach_hang_id: id,
        trang_thai: { [Op.ne]: 'huy' }
      }
    });

    const stats = orderStats[0] || {};
    
    // Lấy nhóm khách hàng đầu tiên nếu có
    const nhomKhachHang = customer.nhomKhachHangList && customer.nhomKhachHangList.length > 0 
      ? customer.nhomKhachHangList[0]
      : null;
    
    // Lấy địa chỉ mặc định
    const defaultAddress = customer.diaChiList && customer.diaChiList.length > 0 
      ? customer.diaChiList[0] 
      : null;
    
    // Lấy thông tin nhân viên phụ trách nếu có
    let nhanVienPhuTrach = null;

    // Kiểm tra nếu phanCongKhachHang là mảng
    if (Array.isArray(customer.phanCongKhachHang) && customer.phanCongKhachHang.length > 0) {
      // Lấy phần tử đầu tiên nếu là mảng
      const phanCong = customer.phanCongKhachHang[0];
      if (phanCong && phanCong.nhan_vien_id) {
        try {
          nhanVienPhuTrach = await NguoiDung.findOne({
            where: { 
              id: phanCong.nhan_vien_id,
              loai_nguoi_dung: 'nhan_vien'
            },
            attributes: ['id', 'ho_ten', 'email', 'so_dien_thoai']
          });
        } catch (error) {
          console.error('Error fetching assigned employee from array:', error);
        }
      }
    } else if (customer.phanCongKhachHang && customer.phanCongKhachHang.nhan_vien_id) {
      // Xử lý nếu là đối tượng đơn lẻ
      try {
        nhanVienPhuTrach = await NguoiDung.findOne({
          where: { 
            id: customer.phanCongKhachHang.nhan_vien_id,
            loai_nguoi_dung: 'nhan_vien'
          },
          attributes: ['id', 'ho_ten', 'email', 'so_dien_thoai']
        });
      } catch (error) {
        console.error('Error fetching assigned employee from object:', error);
      }
    }
    const customerData = {
      id: customer.id,
      ma_khach_hang: `KH${String(customer.id).padStart(5, '0')}`,
      ho_ten: customer.ho_ten,
      email: customer.email,
      so_dien_thoai: customer.so_dien_thoai,
      dia_chi: defaultAddress?.dia_chi || '',
      phuong_xa: defaultAddress?.phuong_xa || '',
      quan_huyen: defaultAddress?.quan_huyen || '',
      tinh_thanh: defaultAddress?.tinh_thanh || '',
      nhomKhachHang: nhomKhachHang,
      nhanVienPhuTrach: nhanVienPhuTrach,
      trang_thai: customer.trang_thai,
      ghi_chu: customer.hoSoCaNhan?.mo_ta || '',
      gioi_tinh: customer.hoSoCaNhan?.gioi_tinh || null,
      ngay_sinh: customer.hoSoCaNhan?.ngay_sinh || null,
      ma_so_thue: customer.hoSoCaNhan?.ma_so_thue || '',
      website: customer.hoSoCaNhan?.website || '',
      tags: customer.tagList || [],
      cong_no_hien_tai: customer.congNo?.tong_cong_no || 0,
      tong_chi_tieu: parseInt(stats.getDataValue('tong_chi_tieu') || 0),
      tong_sl_don_hang: parseInt(stats.getDataValue('tong_sl_don_hang') || 0),
      nguoi_tao: customer.nguoi_tao,
      ngay_tao: customer.createdAt
    };

    res.json({
      success: true,
      data: { customer: customerData }
    });
  } catch (error) {
    console.error('Error in getCustomer:', error);
    throw new AppError(`Failed to fetch customer: ${error.message}`, 500);
  }
};

/**
 * Tạo khách hàng mới
 */
const createCustomer = async (req, res) => {
  const { 
    ho_ten, 
    email, 
    so_dien_thoai, 
    dia_chi, 
    phuong_xa,
    quan_huyen,
    tinh_thanh,
    nhom_khach_hang_id,
    ghi_chu,
    trang_thai,
    nhan_vien_id,
    // Các trường không bắt buộc
    gioi_tinh,
    ngay_sinh,
    ma_so_thue,
    website
  } = req.body;

  // Validate dữ liệu
  if (!ho_ten) {
    throw new AppError('Tên khách hàng là bắt buộc', 400);
  }

  // Kiểm tra email đã tồn tại
  if (email) {
    const existingCustomer = await NguoiDung.findOne({ where: { email } });
    if (existingCustomer) {
      throw new AppError('Email đã được sử dụng', 400);
    }
  }

  // Kiểm tra số điện thoại đã tồn tại
  if (so_dien_thoai) {
    const existingCustomer = await NguoiDung.findOne({ where: { so_dien_thoai } });
    if (existingCustomer) {
      throw new AppError('Số điện thoại đã được sử dụng', 400);
    }
  }

  try {
    // Bắt đầu transaction
    const result = await sequelize.transaction(async (t) => {
      // Tạo người dùng mới
      const newCustomer = await NguoiDung.create({
        ho_ten,
        email,
        so_dien_thoai,
        loai_nguoi_dung: 'khach_hang',
        trang_thai: trang_thai || 'dang_giao_dich',
        nguoi_tao: req.user?.email || 'system'
      }, { transaction: t });

      // Tạo hồ sơ cá nhân với các trường không bắt buộc
      try {
        await db.HoSoCaNhan.create({
          nguoi_dung_id: newCustomer.id,
          mo_ta: ghi_chu,
          gioi_tinh: gioi_tinh || null,
          ngay_sinh: ngay_sinh || null,
          ma_so_thue: ma_so_thue || null,
          website: website || null
        }, { transaction: t });
      } catch (error) {
        console.error('Error creating HoSoCaNhan:', error);
        // Tiếp tục mà không tạo hồ sơ cá nhân
      }

      // Tạo địa chỉ nếu có
      if (dia_chi) {
        try {
          await db.DiaChiNguoiDung.create({
            nguoi_dung_id: newCustomer.id,
            dia_chi,
            phuong_xa,
            quan_huyen,
            tinh_thanh,
            mac_dinh: true
          }, { transaction: t });
        } catch (error) {
          console.error('Error creating DiaChiNguoiDung:', error);
          // Tiếp tục mà không tạo địa chỉ
        }
      }

      // Tạo công nợ
      try {
        await db.CongNoNguoiDung.create({
          nguoi_dung_id: newCustomer.id,
          tong_cong_no: 0
        }, { transaction: t });
      } catch (error) {
        console.error('Error creating CongNoNguoiDung:', error);
        // Tiếp tục mà không tạo công nợ
      }

      // Thêm vào nhóm khách hàng nếu có
      if (nhom_khach_hang_id) {
        try {
          await db.NhomKhachHangNguoiDung.create({
            nguoi_dung_id: newCustomer.id,
            nhom_khach_hang_id
          }, { transaction: t });
        } catch (error) {
          console.error('Error creating NhomKhachHangNguoiDung:', error);
          // Tiếp tục mà không thêm vào nhóm
        }
      }

      // Phân công nhân viên phụ trách nếu có
      if (nhan_vien_id) {
        try {
          // Kiểm tra nhân viên tồn tại
          const employee = await NguoiDung.findOne({
            where: { 
              id: nhan_vien_id,
              loai_nguoi_dung: 'nhan_vien'
            },
            transaction: t
          });

          if (employee) {
            await PhanCongNhanVienPhuTrach.create({
              nguoi_dung_id: newCustomer.id,
              nhan_vien_id,
              ngay_phan_cong: new Date()
            }, { transaction: t });
          }
        } catch (error) {
          console.error('Error creating PhanCongNhanVienPhuTrach:', error);
          // Tiếp tục mà không phân công nhân viên
        }
      }

      return newCustomer;
    });
    // Lấy thông tin khách hàng đã tạo
    const customer = await NguoiDung.findByPk(result.id, {
      include: [
        {
          model: NhomKhachHang,
          as: 'nhomKhachHangList',
          through: { attributes: [] },
          required: false
        },
        {
          model: db.HoSoCaNhan,
          as: 'hoSoCaNhan',
          required: false
        },
        {
          model: db.DiaChiNguoiDung,
          as: 'diaChiList',
          required: false,
          where: { mac_dinh: true },
          limit: 1
        },
        {
          model: PhanCongNhanVienPhuTrach,
          as: 'phanCongKhachHang',
          required: false,
          attributes: ['id', 'nhan_vien_id', 'ngay_phan_cong']
        }
      ]
    });

    // Lấy thông tin nhân viên phụ trách nếu có
    let nhanVienPhuTrach = null;
    if (customer.phanCongKhachHang && customer.phanCongKhachHang.nhan_vien_id) {
      try {
        nhanVienPhuTrach = await NguoiDung.findOne({
          where: { id: customer.phanCongKhachHang.nhan_vien_id },
          attributes: ['id', 'ho_ten', 'email', 'so_dien_thoai']
        });
      } catch (error) {
        console.error('Error fetching assigned employee:', error);
        // Tiếp tục mà không lấy thông tin nhân viên phụ trách
      }
    }
    // Lấy địa chỉ mặc định
    const defaultAddress = customer.diaChiList && customer.diaChiList.length > 0 
      ? customer.diaChiList[0] 
      : null;

    // Chuẩn bị dữ liệu phản hồi
    const customerData = {
      id: customer.id,
      ma_khach_hang: `KH${String(customer.id).padStart(5, '0')}`,
      ho_ten: customer.ho_ten,
      email: customer.email,
      so_dien_thoai: customer.so_dien_thoai,
      dia_chi: defaultAddress?.dia_chi || '',
      phuong_xa: defaultAddress?.phuong_xa || '',
      quan_huyen: defaultAddress?.quan_huyen || '',
      tinh_thanh: defaultAddress?.tinh_thanh || '',
      nhomKhachHang: customer.nhomKhachHangList && customer.nhomKhachHangList.length > 0 
        ? customer.nhomKhachHangList[0] 
        : null,
      trang_thai: customer.trang_thai,
      ghi_chu: customer.hoSoCaNhan?.mo_ta || '',
      gioi_tinh: customer.hoSoCaNhan?.gioi_tinh || null,
      ngay_sinh: customer.hoSoCaNhan?.ngay_sinh || null,
      ma_so_thue: customer.hoSoCaNhan?.ma_so_thue || '',
      website: customer.hoSoCaNhan?.website || '',
      cong_no_hien_tai: 0,
      tong_chi_tieu: 0,
      tong_sl_don_hang: 0,
      nhan_vien_phu_trach: nhanVienPhuTrach
    };

    res.status(201).json({
      success: true,
      message: 'Tạo khách hàng thành công',
      data: { customer: customerData }
    });
  } catch (error) {
    console.error('Error in createCustomer:', error);
    throw new AppError(`Failed to create customer: ${error.message}`, 500);
  }
};

/**
 * Cập nhật khách hàng
 */
const updateCustomer = async (req, res) => {
  const { id } = req.params;
  const { 
    ho_ten, 
    email, 
    so_dien_thoai, 
    dia_chi,
    phuong_xa,
    quan_huyen,
    tinh_thanh,
    nhom_khach_hang_id,
    ghi_chu,
    trang_thai,
    nhan_vien_id,
    // Các trường không bắt buộc
    gioi_tinh,
    ngay_sinh,
    ma_so_thue,
    website
  } = req.body;

  try {
    const customer = await NguoiDung.findByPk(id, {
      include: [
        {
          model: db.HoSoCaNhan,
          as: 'hoSoCaNhan'
        },
        {
          model: db.DiaChiNguoiDung,
          as: 'diaChiList',
          where: { mac_dinh: true },
          required: false
        }
      ]
    });
    
    if (!customer) {
      throw new AppError('Không tìm thấy khách hàng', 404);
    }

    // Kiểm tra xem có phải khách hàng không
    if (customer.loai_nguoi_dung !== 'khach_hang') {
      throw new AppError('Người dùng này không phải là khách hàng', 400);
    }

    // Kiểm tra email đã tồn tại
    if (email && email !== customer.email) {
      const existingCustomer = await NguoiDung.findOne({ where: { email } });
      if (existingCustomer) {
        throw new AppError('Email đã được sử dụng', 400);
      }
    }

    // Kiểm tra số điện thoại đã tồn tại
    if (so_dien_thoai && so_dien_thoai !== customer.so_dien_thoai) {
      const existingCustomer = await NguoiDung.findOne({ where: { so_dien_thoai } });
      if (existingCustomer) {
        throw new AppError('Số điện thoại đã được sử dụng', 400);
      }
    }

    // Bắt đầu transaction
    await sequelize.transaction(async (t) => {
      // Cập nhật thông tin khách hàng
      await customer.update({
        ho_ten: ho_ten || customer.ho_ten,
        email: email || customer.email,
        so_dien_thoai: so_dien_thoai || customer.so_dien_thoai,
        trang_thai: trang_thai || customer.trang_thai,
        nguoi_cap_nhap: req.user?.email || 'system'
      }, { transaction: t });

      // Cập nhật hồ sơ cá nhân với các trường không bắt buộc
      if (customer.hoSoCaNhan) {
        await customer.hoSoCaNhan.update({
          mo_ta: ghi_chu !== undefined ? ghi_chu : customer.hoSoCaNhan.mo_ta,
          gioi_tinh: gioi_tinh !== undefined ? gioi_tinh : customer.hoSoCaNhan.gioi_tinh,
          ngay_sinh: ngay_sinh !== undefined ? ngay_sinh : customer.hoSoCaNhan.ngay_sinh,
          ma_so_thue: ma_so_thue !== undefined ? ma_so_thue : customer.hoSoCaNhan.ma_so_thue,
          website: website !== undefined ? website : customer.hoSoCaNhan.website
        }, { transaction: t });
      } else {
        // Tạo mới nếu chưa có
        await db.HoSoCaNhan.create({
          nguoi_dung_id: customer.id,
          mo_ta: ghi_chu || null,
          gioi_tinh: gioi_tinh || null,
          ngay_sinh: ngay_sinh || null,
          ma_so_thue: ma_so_thue || null,
          website: website || null
        }, { transaction: t });
      }

      // Cập nhật địa chỉ
      if (dia_chi !== undefined) {
        const defaultAddress = customer.diaChiList && customer.diaChiList.length > 0 
          ? customer.diaChiList[0] 
          : null;
          
        if (defaultAddress) {
          // Cập nhật địa chỉ hiện có
          await defaultAddress.update({
            dia_chi,
            phuong_xa: phuong_xa || defaultAddress.phuong_xa,
            quan_huyen: quan_huyen || defaultAddress.quan_huyen,
            tinh_thanh: tinh_thanh || defaultAddress.tinh_thanh
          }, { transaction: t });
        } else if (dia_chi) {
          // Tạo địa chỉ mới nếu chưa có
          await db.DiaChiNguoiDung.create({
            nguoi_dung_id: customer.id,
            dia_chi,
            phuong_xa,
            quan_huyen,
            tinh_thanh,
            mac_dinh: true
          }, { transaction: t });
        }
      }

      // Cập nhật nhóm khách hàng
      if (nhom_khach_hang_id) {
        // Xóa tất cả nhóm hiện tại
        await db.NhomKhachHangNguoiDung.destroy({
          where: { nguoi_dung_id: customer.id },
          transaction: t
        });
        
        // Thêm nhóm mới
        await db.NhomKhachHangNguoiDung.create({
          nguoi_dung_id: customer.id,
          nhom_khach_hang_id
        }, { transaction: t });
      }

      // Cập nhật nhân viên phụ trách
      if (nhan_vien_id !== undefined) {
        // Xóa phân công hiện tại
        await PhanCongNhanVienPhuTrach.destroy({
          where: { nguoi_dung_id: customer.id },
          transaction: t
        });
        
        // Thêm phân công mới nếu có
        if (nhan_vien_id) {
          // Kiểm tra nhân viên tồn tại
          const employee = await NguoiDung.findOne({
            where: { 
              id: nhan_vien_id,
              loai_nguoi_dung: 'nhan_vien'
            },
            transaction: t
          });

          if (employee) {
            await PhanCongNhanVienPhuTrach.create({
              nguoi_dung_id: customer.id,
              nhan_vien_id,
              ngay_phan_cong: new Date()
            }, { transaction: t });
          }
        }
      }
    });

    // Lấy thông tin khách hàng đã cập nhật
    const updatedCustomer = await NguoiDung.findByPk(id, {
      include: [
        {
          model: NhomKhachHang,
          as: 'nhomKhachHangList',
          through: { attributes: [] }
        },
        {
          model: db.HoSoCaNhan,
          as: 'hoSoCaNhan'
        },
        {
          model: db.CongNoNguoiDung,
          as: 'congNo'
        },
        {
          model: db.DiaChiNguoiDung,
          as: 'diaChiList',
          where: { mac_dinh: true },
          required: false,
          limit: 1
        },
        {
          model: PhanCongNhanVienPhuTrach,
          as: 'phanCongKhachHang',
          required: false
        }
      ]
    });

    // Lấy thông tin đơn hàng
    const orderStats = await DonHang.findAll({
      attributes: [
        [sequelize.fn('SUM', sequelize.col('tong_tien')), 'tong_chi_tieu'],
        [sequelize.fn('COUNT', sequelize.col('id')), 'tong_sl_don_hang']
      ],
      where: {
        khach_hang_id: id,
        trang_thai: { [Op.ne]: 'huy' }
      }
    });

    const stats = orderStats[0] || {};
    
    // Lấy nhóm khách hàng đầu tiên nếu có
    const nhomKhachHang = updatedCustomer.nhomKhachHangList && updatedCustomer.nhomKhachHangList.length > 0 
      ? updatedCustomer.nhomKhachHangList[0]
      : null;
      
    // Lấy địa chỉ mặc định
    const defaultAddress = updatedCustomer.diaChiList && updatedCustomer.diaChiList.length > 0 
      ? updatedCustomer.diaChiList[0] 
      : null;

    // Lấy thông tin nhân viên phụ trách nếu có
    let nhanVienPhuTrach = null;
    if (updatedCustomer.phanCongKhachHang) {
      try {
        nhanVienPhuTrach = await NguoiDung.findOne({
          where: { 
            id: updatedCustomer.phanCongKhachHang.nhan_vien_id 
          },
          attributes: ['id', 'ho_ten', 'email', 'so_dien_thoai']
        });
      } catch (error) {
        console.error('Error fetching assigned employee:', error);
        // Tiếp tục mà không lấy thông tin nhân viên phụ trách
      }
    }

    res.json({
      success: true,
      message: 'Cập nhật khách hàng thành công',
      data: { 
        customer: {
          id: updatedCustomer.id,
          ma_khach_hang: `KH${String(updatedCustomer.id).padStart(5, '0')}`,
          ho_ten: updatedCustomer.ho_ten,
          email: updatedCustomer.email,
          so_dien_thoai: updatedCustomer.so_dien_thoai,
          dia_chi: defaultAddress?.dia_chi || '',
          phuong_xa: defaultAddress?.phuong_xa || '',
          quan_huyen: defaultAddress?.quan_huyen || '',
          tinh_thanh: defaultAddress?.tinh_thanh || '',
          nhomKhachHang: nhomKhachHang,
          trang_thai: updatedCustomer.trang_thai,
          ghi_chu: updatedCustomer.hoSoCaNhan?.mo_ta || '',
          cong_no_hien_tai: updatedCustomer.congNo?.tong_cong_no || 0,
          tong_chi_tieu: parseInt(stats.getDataValue('tong_chi_tieu') || 0),
          tong_sl_don_hang: parseInt(stats.getDataValue('tong_sl_don_hang') || 0),
          nguoi_tao: updatedCustomer.nguoi_tao,
          ngay_tao: updatedCustomer.createdAt,
          nhan_vien_phu_trach: nhanVienPhuTrach
        }
      }
    });
  } catch (error) {
    console.error('Error in updateCustomer:', error);
    throw new AppError(`Failed to update customer: ${error.message}`, 500);
  }
};

/**
 * Xóa khách hàng
 */
const deleteCustomer = async (req, res) => {
  const { id } = req.params;

  try {
    const customer = await NguoiDung.findByPk(id);
    if (!customer) {
      throw new AppError('Không tìm thấy khách hàng', 404);
    }

    // Kiểm tra xem có phải khách hàng không
    if (customer.loai_nguoi_dung !== 'khach_hang') {
      throw new AppError('Người dùng này không phải là khách hàng', 400);
    }

    // Kiểm tra xem khách hàng có đơn hàng không
    const orderCount = await DonHang.count({
      where: { khach_hang_id: id }
    });

    if (orderCount > 0) {
      throw new AppError('Không thể xóa khách hàng đã có đơn hàng', 400);
    }

    // Bắt đầu transaction
    await sequelize.transaction(async (t) => {
      // Xóa các bản ghi liên quan
      await db.NhomKhachHangNguoiDung.destroy({
        where: { nguoi_dung_id: id },
        transaction: t
      });
      
      await db.HoSoCaNhan.destroy({
        where: { nguoi_dung_id: id },
        transaction: t
      });
      
      await db.CongNoNguoiDung.destroy({
        where: { nguoi_dung_id: id },
        transaction: t
      });
      
      // Xóa người dùng
      await customer.destroy({ transaction: t });
    });

    res.json({
      success: true,
      message: 'Xóa khách hàng thành công'
    });
  } catch (error) {
    console.error('Error in deleteCustomer:', error);
    throw new AppError(`Failed to delete customer: ${error.message}`, 500);
  }
};

/**
 * Xuất danh sách khách hàng
 */
const exportCustomers = async (req, res) => {
  // TODO: Implement export functionality
  res.json({
    success: true,
    message: 'API xuất khách hàng đang được phát triển'
  });
};

/**
 * Nhập danh sách khách hàng từ file
 */
const importCustomers = async (req, res) => {
  // TODO: Implement import functionality
  res.json({
    success: true,
    message: 'API nhập khách hàng đang được phát triển'
  });
};

module.exports = {
  getCustomers,
  getCustomer,
  createCustomer,
  updateCustomer,
  deleteCustomer,
  exportCustomers,
  importCustomers
};
