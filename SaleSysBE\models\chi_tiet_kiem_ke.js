'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class ChiTietKiemKe extends Model {
    static associate(models) {
      // Quan hệ với phiếu kiểm kê
      if (models.PhieuKiemKe) {
        ChiTietKiemKe.belongsTo(models.PhieuKiemKe, {
          foreignKey: 'phieu_kiem_ke_id',
          as: 'phieuKiemKe'
        });
      }

      // Quan hệ với phiên bản sản phẩm
      if (models.PhienBanSanPham) {
        ChiTietKiemKe.belongsTo(models.PhienBanSanPham, {
          foreignKey: 'phien_ban_san_pham_id',
          as: 'phienBanSanPham'
        });
      }
    }
  }

  ChiTietKiemKe.init({
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    phieu_kiem_ke_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'phieu_kiem_ke',
        key: 'id'
      },
      comment: 'ID phiếu kiểm kê'
    },
    phien_ban_san_pham_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'phien_ban_san_pham',
        key: 'id'
      },
      comment: 'ID phiên bản sản phẩm'
    },
    so_luong_he_thong: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Số lượng theo hệ thống'
    },
    so_luong_thuc_te: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'Số lượng thực tế kiểm được'
    },
    so_luong_lech: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Số lượng chênh lệch (thực tế - hệ thống)'
    },
    gia_von: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0,
      comment: 'Giá vốn tại thời điểm kiểm kê'
    },
    gia_tri_lech: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0,
      comment: 'Giá trị chênh lệch (số lượng lệch * giá vốn)'
    },
    trang_thai: {
      type: DataTypes.ENUM('chua_kiem', 'da_kiem', 'co_lech'),
      allowNull: false,
      defaultValue: 'chua_kiem',
      comment: 'Trạng thái kiểm kê chi tiết'
    },
    ghi_chu: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Ghi chú cho sản phẩm này'
    },
    nguoi_kiem: {
      type: DataTypes.STRING(100),
      allowNull: true,
      comment: 'Người kiểm sản phẩm này'
    },
    ngay_kiem: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Ngày kiểm sản phẩm này'
    },
    ngay_tao: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'Ngày tạo bản ghi'
    },
    ngay_cap_nhap: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'Ngày cập nhật bản ghi'
    }
  }, {
    sequelize,
    modelName: 'ChiTietKiemKe',
    tableName: 'chi_tiet_kiem_ke',
    timestamps: false // Sử dụng custom timestamp fields
  });

  return ChiTietKiemKe;
};
