const mysql = require('mysql2/promise');

async function checkTables() {
  let connection;
  try {
    console.log('🔄 Connecting to MySQL...');
    
    // Tạo connection trực tiếp
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '<PERSON><PERSON><PERSON>27@',
      database: 'sale_system'
    });
    
    console.log('✅ Connected to MySQL');
    
    // Kiểm tra tất cả bảng
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('📊 All tables in database:', tables);
    
    // Kiểm tra bảng payments cụ thể
    const [paymentsCheck] = await connection.execute('SHOW TABLES LIKE "payments"');
    console.log('💰 Payments table check:', paymentsCheck);
    
    if (paymentsCheck.length > 0) {
      // Kiểm tra cấu trúc bảng payments
      const [structure] = await connection.execute('DESCRIBE payments');
      console.log('🏗️ Payments table structure:', structure);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error checking tables:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

checkTables();
