'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ton_kho_phien_ban', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      kho_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'kho_hang',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      phien_ban_san_pham_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'phien_ban_san_pham',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      so_luong_thay_doi: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0
      },
      so_luong_ton: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0
      },
      so_luong_toi_thieu: {
        type: Sequelize.INTEGER,
        allowNull: true,
        defaultValue: 0,
        comment: '<PERSON><PERSON> lượng tồn kho tối thiểu'
      },
      gia_von: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
        defaultValue: 0,
        comment: 'Giá vốn của sản phẩm'
      },
      ma_chung_tu: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nguoi_tao: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nguoi_cap_nhap: {
        type: Sequelize.STRING,
        allowNull: true
      },
      ngay_tao: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      },
      ngay_cap_nhap: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.NOW
      }
    });

    // Add indexes
    await queryInterface.addIndex('ton_kho_phien_ban', ['kho_hang_id']);
    await queryInterface.addIndex('ton_kho_phien_ban', ['phien_ban_san_pham_id']);
    await queryInterface.addIndex('ton_kho_phien_ban', ['kho_hang_id', 'phien_ban_san_pham_id'], {
      unique: true,
      name: 'ton_kho_phien_ban_unique'
    });
    await queryInterface.addIndex('ton_kho_phien_ban', ['so_luong_ton']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ton_kho_phien_ban');
  }
};
