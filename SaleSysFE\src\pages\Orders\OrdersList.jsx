import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Input,
  Select,
  DatePicker,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Dropdown,
  Menu,
  message,
  Modal,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  MoreOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  UserOutlined,
  CalendarOutlined,
  ExportOutlined,
  FilterOutlined
} from '@ant-design/icons';
import { useOrders, useUpdateOrderStatus, useDeleteOrder } from '../../hooks/useOrders';
import { ORDER_STATUS, getOrderStatusInfo } from '../../constants/orderConstants';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const OrdersList = () => {
  const navigate = useNavigate();
  
  // States
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [dateRange, setDateRange] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  // API hooks
  const { data: ordersData, isLoading } = useOrders({
    page: pagination.current,
    limit: pagination.pageSize,
    search: searchText,
    trang_thai: statusFilter,
    tu_ngay: dateRange[0]?.format('YYYY-MM-DD'),
    den_ngay: dateRange[1]?.format('YYYY-MM-DD')
  });

  const updateStatusMutation = useUpdateOrderStatus();
  const deleteOrderMutation = useDeleteOrder();

  const orders = ordersData?.data || [];
  const stats = ordersData?.stats || {};

  // Status options
  const statusOptions = [
    { value: '', label: 'Tất cả trạng thái' },
    ...ORDER_STATUS
  ];

  // Get status config
  const getStatusConfig = (status) => {
    return statusOptions.find(opt => opt.value === status) || { color: 'default', label: status };
  };

  // Handle status change
  const handleStatusChange = (orderId, newStatus) => {
    Modal.confirm({
      title: 'Xác nhận thay đổi trạng thái',
      content: `Bạn có chắc chắn muốn thay đổi trạng thái đơn hàng này?`,
      onOk: () => {
        updateStatusMutation.mutate({ id: orderId, status: newStatus });
      }
    });
  };

  // Handle delete order
  const handleDeleteOrder = (orderId) => {
    Modal.confirm({
      title: 'Xác nhận xóa đơn hàng',
      content: 'Bạn có chắc chắn muốn xóa đơn hàng này? Hành động này không thể hoàn tác.',
      okText: 'Xóa',
      okType: 'danger',
      cancelText: 'Hủy',
      onOk: () => {
        deleteOrderMutation.mutate(orderId);
      }
    });
  };

  // Table columns
  const columns = [
    {
      title: 'Mã đơn hàng',
      dataIndex: 'ma_don_hang',
      key: 'ma_don_hang',
      width: 120,
      render: (text, record) => (
        <Button 
          type="link" 
          onClick={() => navigate(`/orders/${record.id}`)}
          style={{ padding: 0, fontWeight: 500 }}
        >
          {text}
        </Button>
      )
    },
    {
      title: 'Tên khách hàng',
      key: 'customer',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>
            {record.ten_khach_hang || 'Khách lẻ'}
          </div>
          {record.so_dien_thoai && (
            <Text type="secondary" style={{ fontSize: 12 }}>
              {record.so_dien_thoai}
            </Text>
          )}
        </div>
      )
    },
    // {
    //   title: 'Khách hàng',
    //   key: 'customer',
    //   width: 200,
    //   render: (_, record) => (
    //     <div>
    //       <div style={{ fontWeight: 500 }}>
    //         {record.ten_khach_hang || 'Khách lẻ'}
    //       </div>
    //       {record.so_dien_thoai && (
    //         <Text type="secondary" style={{ fontSize: 12 }}>
    //           {record.so_dien_thoai}
    //         </Text>
    //       )}
    //     </div>
    //   )
    // },
    {
      title: 'Ngày đặt',
      dataIndex: 'ngay_dat_hang',
      key: 'ngay_dat_hang',
      width: 120,
      render: (date) => dayjs(date).format('DD/MM/YYYY')
    },
    {
      title: 'Sản phẩm',
      key: 'products',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Text>{record.so_luong_san_pham || 0} SP</Text>
      )
    },
    {
      title: 'Tổng tiền',
      dataIndex: 'tong_tien',
      key: 'tong_tien',
      width: 120,
      align: 'right',
      render: (amount) => (
        <Text strong style={{ color: '#1890ff' }}>
          {amount?.toLocaleString('vi-VN')}đ
        </Text>
      )
    },
    {
      title: 'Trạng thái',
      dataIndex: 'trang_thai',
      key: 'trang_thai',
      width: 120,
      render: (status) => {
        const config = getStatusConfig(status);
        return <Tag color={config.color}>{config.label}</Tag>;
      }
    },
    {
      title: 'Thao tác',
      key: 'actions',
      width: 120,
      align: 'center',
      render: (_, record) => {
        const menuItems = [
          {
            key: 'view',
            icon: <EyeOutlined />,
            label: 'Xem chi tiết',
            onClick: () => navigate(`/orders/${record.id}`)
          },
          {
            key: 'edit',
            icon: <EditOutlined />,
            label: 'Chỉnh sửa',
            onClick: () => navigate(`/orders/${record.id}/edit`)
          },
          { type: 'divider' },
          ...statusOptions.slice(1).map(status => ({
            key: `status-${status.value}`,
            label: `Chuyển thành ${status.label}`,
            disabled: record.trang_thai === status.value,
            onClick: () => handleStatusChange(record.id, status.value)
          })),
          { type: 'divider' },
          {
            key: 'delete',
            icon: <DeleteOutlined />,
            label: 'Xóa đơn hàng',
            danger: true,
            onClick: () => handleDeleteOrder(record.id)
          }
        ];

        return (
          <Space>
            <Tooltip title="Xem chi tiết">
              <Button 
                type="text" 
                icon={<EyeOutlined />} 
                onClick={() => navigate(`/orders/${record.id}`)}
                size="small"
              />
            </Tooltip>
            <Dropdown
              menu={{ items: menuItems }}
              trigger={['click']}
              placement="bottomRight"
            >
              <Button type="text" icon={<MoreOutlined />} size="small" />
            </Dropdown>
          </Space>
        );
      }
    }
  ];

  // Handle table change
  const handleTableChange = (newPagination) => {
    setPagination({
      ...pagination,
      current: newPagination.current,
      pageSize: newPagination.pageSize
    });
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{
        marginBottom: '24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <Title level={3} style={{ margin: 0 }}>
          <ShoppingCartOutlined style={{ marginRight: 8 }} />
          Quản lý đơn hàng
        </Title>
        <Space>
          <Button icon={<ExportOutlined />}>
            Xuất Excel
          </Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => navigate('/orders/create')}
          >
            Tạo đơn hàng
          </Button>
        </Space>
      </div>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Tổng đơn hàng"
              value={stats.total_orders || 0}
              prefix={<ShoppingCartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Chờ xác nhận"
              value={stats.pending_orders || 0}
              valueStyle={{ color: '#faad14' }}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Đã hoàn thành"
              value={stats.completed_orders || 0}
              valueStyle={{ color: '#52c41a' }}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Doanh thu"
              value={stats.total_revenue || 0}
              formatter={(value) => `${value.toLocaleString('vi-VN')}đ`}
              valueStyle={{ color: '#1890ff' }}
              prefix={<DollarOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* Filters */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Input
              placeholder="Tìm theo mã đơn hàng, tên khách hàng, SĐT..."
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              allowClear
            />
          </Col>
          <Col span={4}>
            <Select
              placeholder="Trạng thái"
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: '100%' }}
              allowClear
            >
              {statusOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <RangePicker
              placeholder={['Từ ngày', 'Đến ngày']}
              value={dateRange}
              onChange={setDateRange}
              style={{ width: '100%' }}
              format="DD/MM/YYYY"
            />
          </Col>
          <Col span={6}>
            <Space>
              <Button 
                icon={<FilterOutlined />}
                onClick={() => {
                  setSearchText('');
                  setStatusFilter('');
                  setDateRange([]);
                }}
              >
                Xóa bộ lọc
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Orders Table */}
      <Card>
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={isLoading}
          pagination={{
            ...pagination,
            total: ordersData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} của ${total} đơn hàng`
          }}
          onChange={handleTableChange}
          scroll={{ x: 1000 }}
        />
      </Card>
    </div>
  );
};

export default OrdersList;
