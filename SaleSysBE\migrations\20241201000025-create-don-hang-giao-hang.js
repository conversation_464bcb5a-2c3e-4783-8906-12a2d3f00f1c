'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('don_hang_giao_hang', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      don_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'don_hang',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      ma_dong_goi: {
        type: Sequelize.STRING,
        allowNull: true,
        unique: true
      },
      hinh_thuc_giao_hang: {
        type: Sequelize.ENUM('Nhan_tai_cua_hang', 'Giao_tan_nha', 'Vc_noi_bo', 'Giao_hang_nhanh', 'Viettel_Post'),
        allowNull: true,
        comment: 'Nhan_tai_cua_hang, Giao_tan_nha, Vc_noi_bo, ...'
      },
      trang_thai: {
        type: Sequelize.ENUM('cho_giao', 'dang_giao', 'da_giao', 'that_bai'),
        defaultValue: 'cho_giao'
      },
      ngay_tao: {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      ngay_giao: {
        type: Sequelize.DATE,
        allowNull: true
      },
      ghi_chu: {
        type: Sequelize.TEXT,
        allowNull: true
      }
    });

    // Add indexes
    await queryInterface.addIndex('don_hang_giao_hang', ['don_hang_id']);
    await queryInterface.addIndex('don_hang_giao_hang', ['ma_dong_goi']);
    await queryInterface.addIndex('don_hang_giao_hang', ['trang_thai']);
    await queryInterface.addIndex('don_hang_giao_hang', ['hinh_thuc_giao_hang']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('don_hang_giao_hang');
  }
};
