import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { message } from 'antd';
import Cookies from 'js-cookie';
import { authAPI } from '../services/api';

// Initial state
const initialState = {
  user: null,
  token: null,
  isAuthenticated: false,
  isLoading: true,
  permissions: []
};

// Action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  SET_LOADING: 'SET_LOADING',
  UPDATE_USER: 'UPDATE_USER'
};

// Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        isLoading: true
      };
    
    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        permissions: action.payload.permissions || [],
        isAuthenticated: true,
        isLoading: false
      };
    
    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        user: null,
        token: null,
        permissions: [],
        isAuthenticated: false,
        isLoading: false
      };
    
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...initialState,
        isLoading: false
      };
    
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };
    
    case AUTH_ACTIONS.UPDATE_USER:
      return {
        ...state,
        user: { ...state.user, ...action.payload }
      };
    
    default:
      return state;
  }
};

// Create context
const AuthContext = createContext();

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for existing token on mount
  useEffect(() => {
    const checkAuth = async () => {
      const token = Cookies.get('token');
      
      if (token) {
        try {
          // Verify token with backend
          const response = await authAPI.getMe();
          
          if (response.data.success) {
            // Thêm permissions công nợ tạm thời cho test
            const mockDebtPermissions = [
              'XEM_CONG_NO',
              'SUA_CONG_NO',
              'TAO_PHIEU_THU',
              'TAO_PHIEU_CHI',
              'XEM_BAO_CAO_CONG_NO'
            ];

            const allPermissions = [
              ...(response.data.data.user.permissions || []),
              ...mockDebtPermissions
            ];

            dispatch({
              type: AUTH_ACTIONS.LOGIN_SUCCESS,
              payload: {
                user: response.data.data.user,
                token,
                permissions: allPermissions
              }
            });
          } else {
            // Token is invalid
            Cookies.remove('token');
            dispatch({ type: AUTH_ACTIONS.LOGIN_FAILURE });
          }
        } catch (error) {
          console.error('Auth check failed:', error);
          Cookies.remove('token');
          dispatch({ type: AUTH_ACTIONS.LOGIN_FAILURE });
        }
      } else {
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      }
    };

    checkAuth();
  }, []);

  // Login function
  const login = async (credentials) => {
    try {
      dispatch({ type: AUTH_ACTIONS.LOGIN_START });
      
      const response = await authAPI.login(credentials);
      
      if (response.data.success) {
        const { token, user } = response.data.data;
        
        // Store token in cookie
        Cookies.set('token', token, { expires: 7 }); // 7 days
        
        // Thêm permissions công nợ tạm thời cho test
        const mockDebtPermissions = [
          'XEM_CONG_NO',
          'SUA_CONG_NO',
          'TAO_PHIEU_THU',
          'TAO_PHIEU_CHI',
          'XEM_BAO_CAO_CONG_NO'
        ];

        const allPermissions = [
          ...(user.permissions || []),
          ...mockDebtPermissions
        ];

        dispatch({
          type: AUTH_ACTIONS.LOGIN_SUCCESS,
          payload: {
            user,
            token,
            permissions: allPermissions
          }
        });
        
        message.success('Đăng nhập thành công!');
        return { success: true };
      } else {
        dispatch({ type: AUTH_ACTIONS.LOGIN_FAILURE });
        message.error(response.data.message || 'Đăng nhập thất bại');
        return { success: false, message: response.data.message };
      }
    } catch (error) {
      dispatch({ type: AUTH_ACTIONS.LOGIN_FAILURE });
      const errorMessage = error.response?.data?.message || 'Có lỗi xảy ra khi đăng nhập';
      message.error(errorMessage);
      return { success: false, message: errorMessage };
    }
  };

  // Logout function
  const logout = () => {
    Cookies.remove('token');
    dispatch({ type: AUTH_ACTIONS.LOGOUT });
    message.success('Đăng xuất thành công!');
  };

  // Update user function
  const updateUser = (userData) => {
    dispatch({
      type: AUTH_ACTIONS.UPDATE_USER,
      payload: userData
    });
  };

  // Change password function
  const changePassword = async (passwordData) => {
    try {
      const response = await authAPI.changePassword(passwordData);
      
      if (response.data.success) {
        message.success('Đổi mật khẩu thành công!');
        return { success: true };
      } else {
        message.error(response.data.message || 'Đổi mật khẩu thất bại');
        return { success: false, message: response.data.message };
      }
    } catch (error) {
      const errorMessage = error.response?.data?.message || 'Có lỗi xảy ra khi đổi mật khẩu';
      message.error(errorMessage);
      return { success: false, message: errorMessage };
    }
  };

  // Check if user has permission
  const hasPermission = (permission) => {
    if (!state.isAuthenticated || !permission) return false;
    
    // Admin has all permissions
    if (state.user?.loai_nguoi_dung === 'admin') return true;
    
    // Check if user has the specific permission
    return state.permissions.includes(permission);
  };

  // Check if user has any of the permissions
  const hasAnyPermission = (permissions) => {
    if (!Array.isArray(permissions)) return false;
    return permissions.some(permission => hasPermission(permission));
  };

  // Check if user has role
  const hasRole = (role) => {
    if (!state.isAuthenticated || !role) return false;
    
    const userRoles = state.user?.vaiTroList?.map(r => r.ma_vai_tro) || [];
    return userRoles.includes(role);
  };

  const value = {
    ...state,
    login,
    logout,
    updateUser,
    changePassword,
    hasPermission,
    hasAnyPermission,
    hasRole
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
