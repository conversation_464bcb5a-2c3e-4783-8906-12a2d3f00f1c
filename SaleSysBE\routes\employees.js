const express = require('express');
const { asyncHandler } = require('../middleware/errorHandler');
const { requirePermission } = require('../middleware/auth');
const router = express.Router();

const {
  getEmployees,
  getEmployee,
  createEmployee,
  updateEmployee,
  deleteEmployee,
  assignCustomers,
  getAssignedCustomers
} = require('../controllers/employeeController');

// L<PERSON>y danh sách nhân viên
router.get('/', asyncHandler(getEmployees));

// L<PERSON>y chi tiết nhân viên
router.get('/:id', asyncHandler(getEmployee));

// Tạo nhân viên mới
router.post('/', requirePermission('THEM_NHAN_VIEN'), asyncHandler(createEmployee));

// Cập nhật nhân viên
router.put('/:id', requirePermission('SUA_NHAN_VIEN'), async<PERSON>and<PERSON>(updateEmployee));

// <PERSON><PERSON>a nhân viên
router.delete('/:id', requirePermission('XOA_NHAN_VIEN'), asyncHand<PERSON>(deleteEmployee));

// Phân công khách hàng cho nhân viên
router.post('/:id/assign-customers', requirePermission('PHAN_CONG_NHAN_VIEN'), asyncHandler(assignCustomers));

// Lấy danh sách khách hàng được phân công cho nhân viên
router.get('/:id/assigned-customers', asyncHandler(getAssignedCustomers));

module.exports = router;