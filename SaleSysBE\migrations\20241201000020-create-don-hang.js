'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('don_hang', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      ma_don_hang: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
        comment: 'SONxxxxx'
      },
      khach_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      kho_hang_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'kho_hang',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      nhan_vien_ban_id: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'nguoi_dung',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL'
      },
      nguon_don_hang: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'web, pos, facebook...'
      },
      chinh_sach_gia: {
        type: Sequelize.STRING,
        allowNull: true
      },
      trang_thai: {
        type: Sequelize.ENUM('cho_xu_ly', 'da_xac_nhan', 'da_dong_goi', 'da_giao', 'hoan_thanh', 'huy'),
        defaultValue: 'cho_xu_ly'
      },
      tong_tien: {
        type: Sequelize.DOUBLE,
        allowNull: true,
        defaultValue: 0
      },
      chiet_khau: {
        type: Sequelize.DOUBLE,
        allowNull: true,
        defaultValue: 0
      },
      phi_giao_hang: {
        type: Sequelize.DOUBLE,
        allowNull: true,
        defaultValue: 0
      },
      ma_giam_gia: {
        type: Sequelize.STRING,
        allowNull: true
      },
      tong_phai_tra: {
        type: Sequelize.DOUBLE,
        allowNull: true,
        defaultValue: 0
      },
      tong_da_tra: {
        type: Sequelize.DOUBLE,
        allowNull: true,
        defaultValue: 0
      },
      con_phai_tra: {
        type: Sequelize.DOUBLE,
        allowNull: true,
        defaultValue: 0
      },
      ghi_chu: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      tags: {
        type: Sequelize.STRING,
        allowNull: true
      },
      ngay_ban: {
        type: Sequelize.DATE,
        allowNull: true
      },
      han_giao_hang: {
        type: Sequelize.DATE,
        allowNull: true
      },
      nguoi_tao: {
        type: Sequelize.STRING,
        allowNull: true
      },
      nguoi_cap_nhap: {
        type: Sequelize.STRING,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')
      }
    });

    // Add indexes
    await queryInterface.addIndex('don_hang', ['ma_don_hang']);
    await queryInterface.addIndex('don_hang', ['khach_hang_id']);
    await queryInterface.addIndex('don_hang', ['kho_hang_id']);
    await queryInterface.addIndex('don_hang', ['nhan_vien_ban_id']);
    await queryInterface.addIndex('don_hang', ['trang_thai']);
    await queryInterface.addIndex('don_hang', ['ngay_ban']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('don_hang');
  }
};
